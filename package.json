{"name": "plukto", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mantine/carousel": "^7.13.4", "@mantine/charts": "^7.13.4", "@mantine/code-highlight": "^7.13.4", "@mantine/core": "^7.13.4", "@mantine/dates": "^7.13.4", "@mantine/dropzone": "^7.13.4", "@mantine/form": "^7.13.4", "@mantine/hooks": "^7.13.4", "@mantine/modals": "^7.13.4", "@mantine/notifications": "^7.13.4", "@mantine/nprogress": "^7.13.4", "@mantine/spotlight": "^7.13.4", "@mantine/tiptap": "^7.13.4", "@mui/material": "^6.4.1", "@mui/x-charts-pro": "^7.25.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.20.0", "@tiptap/extension-link": "^2.9.1", "@tiptap/pm": "^2.9.1", "@tiptap/react": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@vercel/speed-insights": "^1.0.14", "@wavesurfer/react": "^1.0.7", "@xyflow/react": "^12.6.4", "apexcharts": "^4.4.0", "bootstrap": "^5.3.3", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^7.1.0", "exceljs": "^4.4.0", "feather-icons-react": "^0.7.1", "framer-motion": "^12.0.6", "html2canvas-pro": "^1.5.8", "js-cookie": "^3.0.5", "jspdf": "^3.0.0", "lucide-react": "^0.482.0", "mantine-react-table": "^2.0.0-beta.7", "next": "15.0.1", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-d3-cloud": "^1.0.6", "react-dom": "^18.2.0", "react-h5-audio-player": "^3.10.0", "react-icons": "^5.5.0", "react-imask": "^7.6.1", "react-markdown": "^10.1.0", "react-markdown-typewriter": "^1.0.2", "react-simple-typewriter": "^5.0.1", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "reactstrap": "^9.2.3", "recharts": "^2.13.3", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sass": "^1.83.4", "shepherd.js": "^14.4.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "wavesurfer.js": "^7.8.8", "xlsx": "^0.18.5"}, "devDependencies": {"autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-plugin-react": "^7.37.5", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^3.4.17"}}