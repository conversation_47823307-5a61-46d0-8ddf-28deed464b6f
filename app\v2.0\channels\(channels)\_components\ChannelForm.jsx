import React from 'react';
import { DateInput } from '@mantine/dates';
import { Select, FileInput } from '@mantine/core';

export const ChannelForm = (mode, tableForm, user, agents, setFiles) => {
  return (
    <>
      <DateInput {...tableForm.getInputProps('date')} key={tableForm.key('date')} label="Tarih" withAsterisk />
      <Select
        {...tableForm.getInputProps('agentId')}
        key={tableForm.key('agentId')}
        label="Agent"
        clearable
        disabled={user.role === 'ÇAĞRI MT'}
        data={
          agents?.map((x) => ({
            label: x.name + ' ' + x.surname,
            value: '' + x.id,
          })) || []
        }
        mt="sm"
      />
      <Select
        {...tableForm.getInputProps('provider')}
        key={tableForm.key('provider')}
        label="Provider"
        clearable
        data={['Sirius']}
        mt="sm"
      />
      {mode === 'add' && (
        <>
          <FileInput
            placeholder="Ses dosyalarını seç"
            label="Ses Dosyaları"
            accept="audio/wav"
            withAsterisk
            multiple
            error={tableForm.errors['soundFiles']}
            onChange={async (files) => {
              setFiles(files);
            }}
          />
        </>
      )}
    </>
  );
};
