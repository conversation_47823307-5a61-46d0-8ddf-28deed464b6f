'use client';

import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { Grid } from '@mantine/core';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import AgentTopPerformers from './_components/AgentTopPerformers';
import AgentErrorsDistribution from './_components/AgentErrorsDistribution';
import AgentAverageScores from './_components/AgentAverageScores';
import AgentPerformanceTable from './_components/AgentPerformanceTable';
import AgentQualityRuleErrors from './_components/AgentQualityRuleErrors';
import AgentFilters from './_components/AgentFilters';
import AgentDashboardSkeleton from './_components/AgentDashboardSkeleton';

export default function AgentsDashboard() {
  const { permissions, fetchAuthClient, user } = useContext(AuthContext);
  const [agents, setAgents] = useState([]);
  const [tenantParameters, setTenantParameters] = useState(null);

  const searchParams = useSearchParams();
  const router = useRouter();
  const channelType = searchParams.get('channelType');
  const filtersBase64 = searchParams.get('filters');

  const [dateFilterValue, setDateFilterValue] = useState([null, null]);
  const [agentIdFilterValue, setAgentIdFilterValue] = useState([]);

  const [initDateType, setInitDateType] = useState(null);

  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState([]);
  const [targetQualityPoint, setTargetQualityPoint] = useState(null);

  useEffect(() => {
    if (filtersBase64) {
      try {
        const binaryString = atob(filtersBase64);
        const byteArray = Uint8Array.from(binaryString, (char) => char.charCodeAt(0));
        const jsonString = new TextDecoder()
          .decode(byteArray)
          .normalize()
          .replace(/i\u0307/g, 'i');
        const initFilters = JSON.parse(jsonString);

       
        setAgentIdFilterValue(initFilters.find((item) => item.id === 'agentId')?.value || []);
  
        if (initFilters.find((item) => item.id === 'date')?.value) {
          setInitDateType('manuel');
          let tempDateValue = initFilters.find((item) => item.id === 'date')?.value;
          setDateFilterValue([new Date(tempDateValue[0]), new Date(tempDateValue[1])]);
        }

        setFilters(initFilters);
      } catch (error) {
        console.error('Error parsing filters:', error);
      }
    }
  }, [filtersBase64]);

  const setFilter = (value, isEmpty, filterType, filterId) => {
    switch (filterType) {

      case 'dateFilterValue':
        setDateFilterValue(value);
        break;
      case 'agentIdFilterValue':
        setAgentIdFilterValue(value);
        break;
    }

    setFilters((prevFilters) => {
      const filtered = prevFilters.filter((filter) => filter.id !== filterId);
      if (isEmpty) {
        return [...filtered];
      } else {
        return [
          ...filtered,
          {
            id: filterId,
            value: value,
          },
        ];
      }
    });
  };

  const clearFilters = () => {
    setDateFilterValue([null, null]);
    setAgentIdFilterValue([]);
   
    setFilters([]);
  };

  const fetchAgents = async () => {
    const response = await fetchAuthClient('Agent/' + channelType, {
      method: 'GET',
    });
    var responseJson = await response.json();
    setAgents(responseJson);
  };

  const fetchTenantParameters = async () => {
    const response = await fetchAuthClient('Tenant/parameters/' + channelType, {
      method: 'GET',
    });
    var responseJson = await response.json();
    setTenantParameters(responseJson);
    setTargetQualityPoint(responseJson.targetedQualityPoint);
  };

  const fetchAgentDashboard = async () => {
    setLoading(true);
    try {
      const response = await fetchAuthClient(`Agent/dashboard/${channelType}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      });

      if (response.ok) {
        const data = await response.json();
        setDashboardData(data);
      } else {
        console.error('Failed to fetch agent dashboard data');
      }
    } catch (error) {
      console.error('Error fetching agent dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateToAgentDetail = (agentId) => {
    const queryParams = new URLSearchParams();
    queryParams.append('channelType', channelType);
    queryParams.append('agentId', agentId);
    router.push(`/v2.0/channels/agents/detail?${queryParams.toString()}`);
  };

  useEffect(() => {
    if (channelType) {
      fetchAgents();
      fetchTenantParameters();
    }
  }, [channelType]);

  useEffect(() => {
    if (channelType && (filters.length > 0 || filtersBase64)) {
      fetchAgentDashboard();
    } else if (channelType) {
      fetchAgentDashboard();
    }
  }, [channelType, filters]);

  if (!permissions.includes(channelType + '.AgentView')) {
    window.location.href = '/401';
    return <></>;
  }

  if (loading && !dashboardData) {
    return <AgentDashboardSkeleton />;
  }

  return (
    <>
      <AgentFilters
        user={user}
        channelType={channelType}
        agents={agents}
       
        dateFilterValue={dateFilterValue}
        agentIdFilterValue={agentIdFilterValue}
        
        initDateType={initDateType}
      
        setFilter={setFilter}
        clearFilters={clearFilters}
      />

      <Grid mt="md">
        {/* Top section with score statistics */}
        <Grid.Col span={{ md: 6 }}>
          <AgentTopPerformers
            agentPointListObj={dashboardData?.agentPointListObj}
            totalCount={dashboardData?.totalCount}
            onAgentClick={navigateToAgentDetail}
          />
        </Grid.Col>

        <Grid.Col span={{ md: 6 }}>
          <AgentAverageScores
            agentPointListObj={dashboardData?.agentPointListObj}
            targetQualityPoint={targetQualityPoint}
          />
        </Grid.Col>

        {/* Middle section with error distribution */}
        <Grid.Col span={{ md: 6 }}>
          <AgentErrorsDistribution
            agentFailArray={dashboardData?.agentFailArray}
            totalCount={dashboardData?.totalCount}
            onAgentClick={navigateToAgentDetail}
          />
        </Grid.Col>

        {/* Quality rule errors section */}
        <Grid.Col span={{ md: 6 }}>
          <AgentQualityRuleErrors
            agentPromptArray={dashboardData?.agentPromptArray}
            onAgentClick={navigateToAgentDetail}
          />
        </Grid.Col>

        {/* Agent performance table */}
        <Grid.Col span={{ md: 12 }}>
          {dashboardData && (
            <AgentPerformanceTable
              agentPointListObj={dashboardData?.agentPointListObj}
              agentFailArray={dashboardData?.agentFailArray}
              agentPromptArray={dashboardData?.agentPromptArray}
              onAgentClick={navigateToAgentDetail}
            />
          )}
        </Grid.Col>
      </Grid>
    </>
  );
}
