'use client';

import React, { useState, forwardRef, useImperativeHandle, useCallback, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import AudioPlayer from 'react-h5-audio-player';
import 'react-h5-audio-player/lib/styles.css';

import WavesurferPlayer from '@wavesurfer/react';
import { ActionIcon, Slider, Box, Progress, Text } from '@mantine/core';
import { IconDownload, IconPlayerPlay, IconPlayerStop, IconVolume, IconVolumeOff } from '@tabler/icons-react';

export const CustomAudioPlayer = forwardRef(({ onCurrentTimeChange, sound, type, maxDuration }, ref) => {
  const [wavesurfer, setWavesurfer] = useState(null);
  const [volume, setVolume] = useState(100);
  const [currentTime, setCurrentTime] = useState(0);
  const [speed, setSpeed] = useState(1);
  const [isWavesurferReady, setIsWavesurferReady] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(maxDuration || 0);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [canPlayThrough, setCanPlayThrough] = useState(false);
  const [bufferProgress, setBufferProgress] = useState(0);
  const [useNativePlayer, setUseNativePlayer] = useState(true); 
  const [nativeAudioReady, setNativeAudioReady] = useState(false);
  
  const speedOptions = [0.25, 0.5, 1, 1.5, 2, 4];
  const h5PlayerRef = useRef(null);
  const nativeAudioRef = useRef(null);

  const debouncedTimeUpdate = useCallback(
    (time) => {
      if (currentTime !== time) {
        setCurrentTime(time);
        if (onCurrentTimeChange) {
          onCurrentTimeChange(time);
        }
      }
    },
    [currentTime, onCurrentTimeChange]
  );

  const formatTime = (timeInSeconds) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes}:${seconds < 10 ? `0${seconds}` : seconds}`;
  };

  const changeSpeed = () => {
    setSpeed((prevSpeed) => {
      const currentIndex = speedOptions.indexOf(prevSpeed);
      const nextIndex = (currentIndex + 1) % speedOptions.length;
      const newSpeed = speedOptions[nextIndex];
      
      if (isWavesurferReady && wavesurfer && !useNativePlayer) {
        wavesurfer.setPlaybackRate(newSpeed);
      } else if (nativeAudioRef.current) {
        nativeAudioRef.current.playbackRate = newSpeed;
      } else if (h5PlayerRef.current?.audio.current) {
        h5PlayerRef.current.audio.current.playbackRate = newSpeed;
      }
      return newSpeed;
    });
  };

  const handlePlayPause = () => {
    if (isWavesurferReady && wavesurfer && !useNativePlayer) {
      wavesurfer.playPause();
    } else if (nativeAudioRef.current) {
      if (isPlaying) {
        nativeAudioRef.current.pause();
      } else {
        nativeAudioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    } else if (h5PlayerRef.current?.audio.current) {
      if (isPlaying) {
        h5PlayerRef.current.audio.current.pause();
      } else {
        h5PlayerRef.current.audio.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleResultClick = (startTime) => {
    if (isWavesurferReady && wavesurfer && !useNativePlayer) {
      wavesurfer.seekTo(startTime / wavesurfer.getDuration());
      wavesurfer.play();
    } else if (nativeAudioRef.current) {
      nativeAudioRef.current.currentTime = startTime;
      nativeAudioRef.current.play();
      setIsPlaying(true);
    } else if (h5PlayerRef.current?.audio.current) {
      h5PlayerRef.current.audio.current.currentTime = startTime;
      h5PlayerRef.current.audio.current.play();
      setIsPlaying(true);
    }
  };

  const updateBufferProgress = (audio) => {
    if (audio.buffered.length > 0 && audio.duration) {
      const bufferedEnd = audio.buffered.end(audio.buffered.length - 1);
      const progress = (bufferedEnd / audio.duration) * 100;
      setBufferProgress(progress);
    }
  };

  useEffect(() => {
    if (nativeAudioRef.current) {
      const audio = nativeAudioRef.current;
      
      const handleLoadStart = () => {
        setLoadingProgress(0);
        setCanPlayThrough(false);
        setNativeAudioReady(false);
      };

      const handleProgress = () => {
        updateBufferProgress(audio);
      };

      const handleLoadedMetadata = () => {
        setDuration(audio.duration);
        audio.volume = volume / 100;
        audio.playbackRate = speed;
      };

      const handleCanPlay = () => {
        setCanPlayThrough(true);
        setLoadingProgress(100);
        setNativeAudioReady(true);
      };

      const handleTimeUpdate = () => {
        debouncedTimeUpdate(audio.currentTime);
        updateBufferProgress(audio);
      };

      const handlePlay = () => setIsPlaying(true);
      const handlePause = () => setIsPlaying(false);

      audio.addEventListener('loadstart', handleLoadStart);
      audio.addEventListener('progress', handleProgress);
      audio.addEventListener('loadedmetadata', handleLoadedMetadata);
      audio.addEventListener('canplay', handleCanPlay);
      audio.addEventListener('timeupdate', handleTimeUpdate);
      audio.addEventListener('play', handlePlay);
      audio.addEventListener('pause', handlePause);

      return () => {
        audio.removeEventListener('loadstart', handleLoadStart);
        audio.removeEventListener('progress', handleProgress);
        audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
        audio.removeEventListener('canplay', handleCanPlay);
        audio.removeEventListener('timeupdate', handleTimeUpdate);
        audio.removeEventListener('play', handlePlay);
        audio.removeEventListener('pause', handlePause);
      };
    }
  }, [volume, speed, debouncedTimeUpdate]);

  const onReady = (ws) => {
    if (nativeAudioRef.current) {
      const audio = nativeAudioRef.current;
      const currentTimeFromAudio = audio.currentTime;
      const wasPlaying = !audio.paused;
      
      audio.pause();
      
      ws.seekTo(currentTimeFromAudio / ws.getDuration());
      ws.setVolume(volume / 100);
      ws.setPlaybackRate(speed);
      
      if (wasPlaying) {
        ws.play();
      }
    }

    let lastUpdateTime = 0;
    const updateInterval = 100;

    ws.on('audioprocess', () => {
      const currentTimeTemp = ws.getCurrentTime();
      const now = Date.now();

      if (now - lastUpdateTime >= updateInterval) {
        debouncedTimeUpdate(currentTimeTemp);
        lastUpdateTime = now;
      }
    });

    ws.on('play', () => setIsPlaying(true));
    ws.on('pause', () => setIsPlaying(false));

    setWavesurfer(ws);
    setIsWavesurferReady(true);
    
    setTimeout(() => {
      if (bufferProgress > 50 || canPlayThrough) {
        setUseNativePlayer(false);
      }
    }, 2000);
  };

  useEffect(() => {
    if (isWavesurferReady && (bufferProgress > 70 || canPlayThrough)) {
      setUseNativePlayer(false);
    }
  }, [bufferProgress, canPlayThrough, isWavesurferReady]);

  useImperativeHandle(ref, () => ({ handleResultClick: handleResultClick }));

  return (
    <>

      {useNativePlayer && (
        <div style={{ marginBottom: '16px', height: '100px', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <audio
            ref={nativeAudioRef}
            src={sound}
            preload="metadata"
            style={{ display: 'none' }}
          />
          
          {!nativeAudioReady ? (
            <Text size="sm" c="dimmed" ta="center">
              Hızlı başlatma modu - Ses dosyası yükleniyor...
              <br />
              <Text size="xs" c="dimmed" mt="xs">
                Dalga görünümü arka planda hazırlanıyor
              </Text>
            </Text>
          ) : (
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Text size="sm" c="green" ta="center" mb="sm">
                Hızlı başlatma modu hazır!
                <br />
                <Text size="xs" c="dimmed" mt="xs">
                  Oynatmaya başlayabilirsiniz
                </Text>
              </Text>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ActionIcon
                  color="teal"
                  size="lg"
                  onClick={handlePlayPause}
                >
                  {isPlaying ? <IconPlayerStop /> : <IconPlayerPlay />}
                </ActionIcon>
                
                <ActionIcon color="teal" onClick={changeSpeed}>
                  {speed}x
                </ActionIcon>
                
                <Text size="xs" c="dimmed">
                  {formatTime(parseInt(currentTime))} / {formatTime(parseInt(duration))}
                </Text>
              </div>
            </div>
          )}
        </div>
      )}

      {!isWavesurferReady && !useNativePlayer && (
        <div style={{ marginBottom: '16px' }}>
          <AudioPlayer
            ref={h5PlayerRef}
            src={sound}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onListen={(e) => {
              debouncedTimeUpdate(e.target.currentTime);
            }}
            onLoadedMetaData={(e) => {
              setDuration(e.target.duration);
              e.target.volume = volume / 100;
              e.target.playbackRate = speed;
            }}
            showSkipControls={false}
            showJumpControls={false}
            customAdditionalControls={[]}
            customVolumeControls={[]}
            style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '4px'
            }}
          />
        </div>
      )}

      <div style={{ display: isWavesurferReady && !useNativePlayer ? 'block' : 'none' }}>
        <WavesurferPlayer
          height={100}
          barWidth={1}
          splitChannels={type === 'stereo' ? true : false}
          waveColor="purple"
          progressColor="teal"
          url={sound}
          onReady={onReady}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onSeeking={() => {
            if (wavesurfer) {
              let tempCurrentTime = wavesurfer.getCurrentTime();
              setCurrentTime(tempCurrentTime);
              if (onCurrentTimeChange) {
                onCurrentTimeChange(tempCurrentTime);
              }
            }
          }}
        />
        <Text size="xs" c="green" mt="xs">
          Dalga görünümü aktif - Gelişmiş kontroller
          </Text>
        </div>

      {/* Player Controls */}
      <div style={{ display: 'flex', alignItems: 'center', marginTop: '16px' }}>
        <ActionIcon
          color="teal"
          onClick={handlePlayPause}
        >
          {isPlaying ? <IconPlayerStop /> : <IconPlayerPlay />}
        </ActionIcon>
        
        <ActionIcon ms="md" me="md" color="teal" onClick={changeSpeed}>
          {speed}x
        </ActionIcon>
        
        <ActionIcon
          ms="md"
          color="teal"
          onClick={() => {
            window.open(sound, '_blank');
          }}
        >
          <IconDownload />
        </ActionIcon>
        
        <Box ms="md" me="md">
          {formatTime(parseInt(currentTime))}
          {' - '}
          {formatTime(parseInt(duration))}
        </Box>
        
        {/* Volume Control */}
        <IconVolumeOff />
        <Slider
          ms="md"
          me="md"
          style={{ width: '250px' }}
          value={volume}
          onChange={(value) => {
            setVolume(value);
            if (isWavesurferReady && wavesurfer && !useNativePlayer) {
              wavesurfer.setVolume(value / 100);
            } else if (nativeAudioRef.current) {
              nativeAudioRef.current.volume = value / 100;
            } else if (h5PlayerRef.current?.audio.current) {
              h5PlayerRef.current.audio.current.volume = value / 100;
            }
          }}
          min={0}
          max={100}
        />
        <IconVolume />
        
        {/* Player Mode Indicator */}
        <Text size="xs" c="dimmed" ms="md">
          {useNativePlayer ? 'Hızlı Mod' : 'Dalga Modu'}
        </Text>
      </div>

    
    </>
  );
});

CustomAudioPlayer.propTypes = {
  onCurrentTimeChange: PropTypes.func,
  sound: PropTypes.string.isRequired,
  type: PropTypes.string,
  maxDuration: PropTypes.number,
};