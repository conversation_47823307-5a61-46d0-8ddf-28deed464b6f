import React from 'react';
import { Box, Card, Group, HoverCard, SimpleGrid, Text } from '@mantine/core';
import { IconClock } from '@tabler/icons-react';

const AverageDuration = ({ setFilter, setDurationFilterValue, dashboardData, tenantParameters, label, classes }) => {
  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <HoverCard width={350} shadow="md" withinPortal position="bottom">
        <HoverCard.Target>
          <Box style={{ cursor: 'pointer' }}>
            <Text c="dimmed" size="md" tt="uppercase" fw={700}>
              Ortalama {label} Süresi
            </Text>
            <Group
              justify="space-between"
              style={{
                marginTop: 'auto',
              }}
            >
              <Group align="flex-end" gap="xs">
                <Text fz="xl" fw={700}>
                  {Math.round(dashboardData.meanOfDuration)} sn
                </Text>
              </Group>
              <IconClock size={22} className={classes.icon} stroke={1.5} />
            </Group>
          </Box>
        </HoverCard.Target>
        <HoverCard.Dropdown p="xs">
          <Text fw={600} size="sm" mb="xs">
            Detaylı Süre İstatistikleri
          </Text>
          <SimpleGrid cols={{ base: 1, md: 4 }} mt="xs">
            <Box
              style={{
                borderBottomColor: 'purple',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter([dashboardData.maxOfDuration, 10000], false, setDurationFilterValue, 'duration');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                En Uzun
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700} size="sm">
                  {Math.round(dashboardData.maxOfDuration)} sn
                </Text>
              </Group>
            </Box>
            <Box
              style={{
                borderBottomColor: 'teal',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter([tenantParameters.maxACHT, 10000], false, setDurationFilterValue, 'duration');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                {'>='}
                {tenantParameters.maxACHT} Sn
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700} size="sm">
                  {dashboardData.passMaxACHTCount.toLocaleString('tr-TR')} {label}
                </Text>
              </Group>
            </Box>
            <Box
              style={{
                borderBottomColor: 'orange',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
                width: '80px',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter(
                  [tenantParameters.minACHT + 1, tenantParameters.maxACHT - 1],
                  false,
                  setDurationFilterValue,
                  'duration'
                );
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                {tenantParameters.minACHT}
                {'>'}
                {'<'}
                {tenantParameters.maxACHT} Sn
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700} size="sm">
                  {dashboardData.betweenMaxMinACHTCount.toLocaleString('tr-TR')} {label}
                </Text>
              </Group>
            </Box>
            <Box
              style={{
                borderBottomColor: 'teal',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter([0, tenantParameters.minACHT], false, setDurationFilterValue, 'duration');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                {'<='}
                {tenantParameters.minACHT} Sn
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700} size="sm">
                  {dashboardData.unPassMinACHTCount.toLocaleString('tr-TR')} {label}
                </Text>
              </Group>
            </Box>
          </SimpleGrid>
        </HoverCard.Dropdown>
      </HoverCard>
    </Card>
  );
};

export default AverageDuration;
