"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>ton, Select, Drawer, MultiSelect, TextInput } from "@mantine/core";
import { DateInput } from "@mantine/dates";


export default function CallsFilterForm({ isOpen, onClose, dashboardResponse, agents, setColumnFilters }) {
    const [filters, setFilters] = useState({
        dateRange: [null, null],
        selectedAgents: undefined,
        selectedMainCategory: undefined,
        selectedSubCategory: undefined,
        scoreMin: undefined,
        scoreMax: undefined,
        durationMin: undefined,
        durationMax: undefined,
        maxSlienceMin: undefined,
        maxSlienceMax: undefined,
        identifier: undefined,
        selectedAnalysis: undefined,
        selectedType: undefined,
        selectedManuelStatus: undefined,
    });
    const [agentOptions, setAgentOptions] = useState([]);
    const [mainCategoryOptions, setMainCategoryOptions] = useState([]);
    const [subCategoryOptions, setSubCategoryOptions] = useState([]);
    const [analysisOptions, setAnalysisOptions] = useState([]);

    // Agent seçenekleri
    useEffect(() => {
        if (agents) {
            setAgentOptions(
                agents.map((agent) => ({
                    value: agent.id.toString(),
                    label: `${agent.name} ${agent.surname}`,
                }))
            );
        }
    }, [agents]);

    // Kategori, alt kategori ve analiz seçenekleri
    useEffect(() => {
        if (dashboardResponse) {
            const mainCats = Object.keys(dashboardResponse.categoryMap || {}).map((key) => ({
                label: key,
                value: key,
            }));
            setMainCategoryOptions(mainCats);

            const subCats = Object.keys(dashboardResponse.subCategoryMap || {}).map((key) => ({
                label: key,
                value: key,
            }));
            setSubCategoryOptions(subCats);

            const analysisOpts = Object.keys(dashboardResponse.promptFailDict || {}).map((key) => ({
                label: key,
                value: key,
            }));
            setAnalysisOptions(analysisOpts);
        }
    }, [dashboardResponse]);

    const handleApplyFilters = () => {
        const formattedFilters = [];

        // Tarih aralığı filter (başlangıç ve bitiş)
        if (filters.dateRange[0] || filters.dateRange[1]) {
            formattedFilters.push({
                id: "date",
                value: [filters.dateRange[0] ? new Date(filters.dateRange[0]) : null, filters.dateRange[1] ? new Date(filters.dateRange[1]) : null],
            });
        }
        // Agent filter
        if (filters.selectedAgents && filters.selectedAgents.length > 0) {
            formattedFilters.push({
                id: "agentId",
                value: filters.selectedAgents,
            });
        }
        // Ana kategori filterf
        if (filters.selectedMainCategory) {
            formattedFilters.push({
                id: "category",
                value: [filters.selectedMainCategory],
            });
        }
        // Alt kategori filter
        if (filters.selectedSubCategory) {
            formattedFilters.push({
                id: "subCategory",
                value: [filters.selectedSubCategory],
            });
        }

        if (filters.scoreMin || filters.scoreMax) {
            formattedFilters.push({
                id: "point",
                value: [filters.scoreMin ? parseInt(filters.scoreMin) : 0, filters.scoreMax ? parseInt(filters.scoreMax) : 100],
            });
        }
        if (filters.durationMin || filters.durationMax) {
            formattedFilters.push({
                id: "duration",
                value: [filters.durationMin ? parseInt(filters.durationMin) : 0, filters.durationMax ? parseInt(filters.durationMax) : parseInt(dashboardResponse.maxOfCallDuration)],
            });
        }
        if (filters.maxSlienceMin || filters.maxSlienceMax) {
            formattedFilters.push({
                id: "maxSlience",
                value: [filters.maxSlienceMin ? parseInt(filters.maxSlienceMin) : 0, filters.maxSlienceMax ? parseInt(filters.maxSlienceMax) : parseInt(dashboardResponse.maxOfCallSlience)],
            });
        }

        // Çağrı Id filter
        if (filters.identifier) {
            formattedFilters.push({
                id: "identifier",
                value: filters.identifier,
            });
        }
        // Analiz filter
        if (filters.selectedAnalysis && filters.selectedAnalysis.length > 0) {
            formattedFilters.push({
                id: "analysis",
                value: filters.selectedAnalysis,
            });
        }
        // Tür filter
        if (filters.selectedType) {
            formattedFilters.push({
                id: "type",
                value: filters.selectedType,
            });
        }
        // Manuel durum filter
        if (filters.selectedManuelStatus && filters.selectedManuelStatus.length > 0) {
            formattedFilters.push({
                id: "manuelStatus",
                value: filters.selectedManuelStatus,
            });
        }

        setColumnFilters(formattedFilters);
        onClose();
    };

    return (
        <Drawer
            opened={isOpen}
            onClose={onClose}
            title="Filtreler"
            padding="xl"
            size="md"
            position="right"
        >
            <div className="flex flex-col h-full">
                <motion.div
                    className="flex-grow flex flex-col space-y-6 overflow-auto p-6 pb-24"
                    initial={{ y: -20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: 20, opacity: 0 }}
                >

                    <DateInput
                        mb={"md"}
                        label="Başlangıç Tarihi"
                        placeholder="Başlangıç Tarihi"
                        value={filters.dateRange[0]}
                        onChange={(value) => setFilters({ ...filters, dateRange: [value, filters.dateRange[1]] })}
                        clearable
                    />
                    <DateInput
                        label="Bitiş Tarihi"
                        placeholder="Bitiş Tarihi"
                        value={filters.dateRange[1]}
                        onChange={(value) => setFilters({ ...filters, dateRange: [filters.dateRange[0], value] })}
                        clearable
                        mb={"md"}
                    />

                    {/* Agentlar */}
                    <MultiSelect
                        label="Agentlar"
                        mb={"md"}
                        data={agentOptions}
                        placeholder="Agent Seçin"
                        value={filters.selectedAgents}
                        onChange={(value) => setFilters({ ...filters, selectedAgents: value })}
                        searchable
                        clearable
                        className="w-full"
                    />

                    {/* Ana Kategori */}
                    <Select
                        label="Ana Kategori"
                        mb={"md"}
                        data={mainCategoryOptions}
                        placeholder="Kategori Seçin"
                        value={filters.selectedMainCategory}
                        onChange={(value) =>
                            setFilters({ ...filters, selectedMainCategory: value, selectedSubCategory: "" })
                        }
                        searchable
                        clearable
                        className="w-full"
                    />

                    {/* Alt Kategori */}
                    <Select
                        label="Alt Kategori"
                        mb={"md"}
                        data={subCategoryOptions}
                        placeholder="Alt Kategori Seçin"
                        value={filters.selectedSubCategory}
                        onChange={(value) => setFilters({ ...filters, selectedSubCategory: value })}
                        searchable
                        clearable
                        className="w-full"
                    />

                    {/* Puan Aralığı */}
                    <div className="w-full">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Puan Aralığı</label>
                        <div className="flex space-x-4">
                            <TextInput
                                type="number"
                                mb={"md"}
                                placeholder="Min"
                                value={filters.scoreMin}
                                onChange={(e) =>
                                    setFilters({ ...filters, scoreMin: Number(e.target.value) })
                                }
                                className="w-1/2"
                            />
                            <TextInput
                                type="number"
                                mb={"md"}
                                placeholder="Max"
                                value={filters.scoreMax}
                                onChange={(e) =>
                                    setFilters({ ...filters, scoreMax: Number(e.target.value) })
                                }
                                className="w-1/2"
                            />
                        </div>
                    </div>

                    {/* Süre Aralığı (Saniye) */}
                    <div className="w-full">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Süre Aralığı (Saniye)</label>
                        <div className="flex space-x-4">
                            <TextInput
                                type="number"
                                mb={"md"}
                                placeholder="Min"
                                value={filters.durationMin}
                                onChange={(e) =>
                                    setFilters({ ...filters, durationMin: Number(e.target.value) })
                                }
                                className="w-1/2"
                            />
                            <TextInput
                                type="number"
                                mb={"md"}
                                placeholder="Max"
                                value={filters.durationMax}
                                onChange={(e) =>
                                    setFilters({ ...filters, durationMax: Number(e.target.value) })
                                }
                                className="w-1/2"
                            />
                        </div>
                    </div>

                    {/* Sessizlik Aralığı */}
                    <div className="w-full">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Sessizlik Aralığı</label>
                        <div className="flex space-x-4">
                            <TextInput
                                type="number"
                                mb={"md"}
                                placeholder="Min"
                                value={filters.maxSlienceMin}
                                onChange={(e) =>
                                    setFilters({ ...filters, maxSlienceMin: Number(e.target.value) })
                                }
                                className="w-1/2"
                            />
                            <TextInput
                                type="number"
                                mb={"md"}
                                placeholder="Max"
                                value={filters.maxSlienceMax}
                                onChange={(e) =>
                                    setFilters({ ...filters, maxSlienceMax: Number(e.target.value) })
                                }
                                className="w-1/2"
                            />
                        </div>
                    </div>

                    {/* Çağrı Id */}
                    <TextInput
                        type="text"
                        label="Çağrı Id"
                        mb={"md"}
                        placeholder="Çağrı Id"
                        value={filters.identifier}
                        onChange={(e) =>
                            setFilters({ ...filters, identifier: e.target.value })
                        }
                        className="w-full"
                    />

                    {/* Analiz */}
                    <MultiSelect
                        label="Analiz"
                        mb={"md"}
                        data={analysisOptions}
                        placeholder="Analiz Seçin"
                        value={filters.selectedAnalysis}
                        onChange={(value) =>
                            setFilters({ ...filters, selectedAnalysis: value })
                        }
                        searchable
                        clearable
                        className="w-full"
                    />

                    {/* Tür */}
                    <Select
                        label="Tür"
                        mb={"md"}
                        data={["mono", "stereo"]}
                        placeholder="Tür Seçin"
                        value={filters.selectedType}
                        onChange={(value) =>
                            setFilters({ ...filters, selectedType: value })
                        }
                        searchable
                        clearable
                        className="w-full"
                    />

                    {/* Durum */}
                    <MultiSelect
                        label="Durum"
                        mb={"md"}
                        data={["AI", "Düzeltme", "Doğruluk", "Analiz", "Kalite Kontrol", "Tamamlandı"]}
                        placeholder="Durum Seçin"
                        value={filters.selectedManuelStatus}
                        onChange={(value) =>
                            setFilters({ ...filters, selectedManuelStatus: value })
                        }
                        searchable
                        clearable
                        className="w-full"
                    />
                </motion.div>
                <Button
                    variant="gradient"
                    mb={"md"}
                    style={{ width: "100%" }}
                    gradient={{ from: "blue", to: "cyan", deg: 45 }}
                    onClick={handleApplyFilters}
                >
                    Filtreyi Uygula
                </Button>
            </div>
        </Drawer>
    );
}
