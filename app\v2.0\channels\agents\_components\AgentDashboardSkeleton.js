'use client';

import React from 'react';
import { Grid, Skeleton, Card, Box, Group, Stack } from '@mantine/core';

export default function AgentDashboardSkeleton() {
  return (
    <Grid mt="md" className='!mt-20'>
      <Grid.Col span={{ md: 6 }}>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Card.Section withBorder inheritPadding py="xs">
            <Skeleton height={30} width="60%" mb="sm" />
          </Card.Section>
          <Box p="md">
            {[...Array(5)].map((_, index) => (
              <Group key={index} position="apart" mb="md" noWrap>
                <Skeleton height={20} width="40%" radius="md" />
                <Skeleton height={20} width="20%" radius="md" />
                <Skeleton height={20} width="15%" radius="md" />
              </Group>
            ))}
          </Box>
        </Card>
      </Grid.Col>

      <Grid.Col span={{ md: 6 }}>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Card.Section withBorder inheritPadding py="xs">
            <Skeleton height={30} width="60%" mb="sm" />
          </Card.Section>
          <Box p="md" h={250}>
            <Skeleton height="100%" width="100%" radius="md" />
          </Box>
        </Card>
      </Grid.Col>

      <Grid.Col span={{ md: 6 }}>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Card.Section withBorder inheritPadding py="xs">
            <Skeleton height={30} width="60%" mb="sm" />
          </Card.Section>
          <Box p="md" h={250}>
            <Skeleton height="100%" width="100%" radius="md" />
          </Box>
        </Card>
      </Grid.Col>

      <Grid.Col span={{ md: 6 }}>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Card.Section withBorder inheritPadding py="xs">
            <Skeleton height={30} width="60%" mb="sm" />
          </Card.Section>
          <Box p="md">
            <Stack spacing="md">
              {[...Array(4)].map((_, index) => (
                <Group key={index} position="apart" noWrap>
                  <Skeleton height={20} width="60%" radius="md" />
                  <Skeleton height={20} width="20%" radius="md" />
                </Group>
              ))}
            </Stack>
          </Box>
        </Card>
      </Grid.Col>

      <Grid.Col span={{ md: 12 }}>
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Card.Section withBorder inheritPadding py="xs">
            <Skeleton height={30} width="40%" mb="sm" />
          </Card.Section>
          <Box p="md">
            <Group position="apart" mb="md" noWrap>
              {[...Array(5)].map((_, index) => (
                <Skeleton key={index} height={25} width="18%" radius="md" />
              ))}
            </Group>
            {[...Array(8)].map((_, rowIndex) => (
              <Group key={rowIndex} position="apart" mb="md" noWrap>
                {[...Array(5)].map((_, colIndex) => (
                  <Skeleton key={colIndex} height={20} width="18%" radius="md" />
                ))}
              </Group>
            ))}
          </Box>
        </Card>
      </Grid.Col>
    </Grid>
  );
}
