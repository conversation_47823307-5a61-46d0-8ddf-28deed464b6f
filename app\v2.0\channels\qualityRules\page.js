'use client';

import React, { useState, useEffect, useContext } from 'react';
import { Button, Card, Grid, Textarea, Text, ActionIcon, Tooltip, Autocomplete, FileInput, Modal } from '@mantine/core';
import { AuthContext } from '@/common/contexts/AuthContext';
import { TenantTable } from '@/common/components/TenantTable';
import { useSearchParams } from 'next/navigation';
import { IconMinus, IconPlus, IconUpload, IconX } from '@tabler/icons-react';

export default function CallsRules() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const searchParams = useSearchParams();
  const channelType = searchParams.get('channelType');
  const [qualityRulesResponse, setQualityRulesResponse] = useState(null);
  const [confirmModal, setConfirmModal] = useState({ opened: false, action: null, ruleId: null, type: null, ruleName: '' });
  const [positiveFiles, setPositiveFiles] = useState([]);
  const [negativeFiles, setNegativeFiles] = useState([]);

  const fetchRules = async () => {
    const response = await fetchAuthClient('Prompt/' + channelType, { method: 'GET' });
    if (response.ok) {
      const data = await response.json();
      // console.log("data", data);
      setQualityRulesResponse(data);
    }
  };

  const handleRuleAction = async () => {
    if (confirmModal.action === 'activate') {
      await fetchAuthClient('Prompt/' + confirmModal.ruleId + '/active/' + channelType);
    } else if (confirmModal.action === 'deactivate') {
      await fetchAuthClient('Prompt/' + confirmModal.ruleId + '/deactive/' + channelType);
    }
    setConfirmModal({ opened: false, action: null, ruleId: null, type: null, ruleName: '' });
    await fetchRules();
  };

  useEffect(() => {
    fetchRules();
  }, [channelType]);

  const columns = [
    {
      id: 'createdAt',
      accessorKey: 'createdAt',
      header: 'Tarih',
      enableSorting: true,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => (
        <>
          <small>{new Date(row.original.createdAt).toLocaleString('tr-TR')}</small>
        </>
      ),
    },
    {
      id: 'userNameSurname',
      accessorKey: 'userNameSurname',
      header: 'Kullanıcı',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{row.original.userNameSurname}</>,
    },
    {
      id: 'category',
      accessorKey: 'category',
      header: 'Ana Kategori',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{row.original.category}</>,
    },
    {
      id: 'subCategory',
      accessorKey: 'subCategory',
      header: 'Alt Kategori',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{row.original.category}</>,
    },
    {
      id: 'status',
      accessorKey: 'status',
      header: 'Durum',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{row.original.status}</>,
    },
  ];

  const form = (mode, tableForm) => {
    return (
      <>
        <Autocomplete
          {...tableForm.getInputProps('category')}
          key={tableForm.key('category')}
          label="Ana Kategori"
          withAsterisk
          data={qualityRulesResponse ? qualityRulesResponse.map((x) => x.category) : []}
          mt="md"
          mb="md"
        />
        <Autocomplete
          {...tableForm.getInputProps('subCategory')}
          key={tableForm.key('subCategory')}
          label="Alt Kategori"
          withAsterisk
          data={
            qualityRulesResponse
              ? Array.from(
                  new Set(
                    qualityRulesResponse.flatMap((category) => category.subCategories.map((sub) => sub.subCategory))
                  )
                )
              : []
          }
          mt="md"
          mb="md"
        />
        <Textarea
          rows={5}
          {...tableForm.getInputProps('description')}
          key={tableForm.key('description')}
          label="Açıklama"
          withAsterisk
          mt="md"
          mb="md"
        />
        {channelType === 'Call' && (
          <>
            <FileInput
              placeholder="Olumlu Ses dosyalarını seç (Maksimum 5 dosya)"
              label="Olumlu Ses Dosyaları"
              accept="audio/wav"
              multiple
              error={
                positiveFiles && positiveFiles.length > 5 
                  ? "Maksimum 5 dosya yükleyebilirsiniz" 
                  : tableForm.errors['positiveFiles']
              }
              onChange={async (files) => {
                if (files && files.length > 5) {
                  tableForm.setErrors({ 'positiveFiles': 'Maksimum 5 dosya yükleyebilirsiniz' });
                } else {
                  tableForm.clearErrors('positiveFiles');
                  setPositiveFiles(files || []);
                }
              }}
              rightSection={
                positiveFiles && positiveFiles.length > 0 ? (
                  <ActionIcon color="red" onClick={() => {
                    setPositiveFiles([]);
                    tableForm.clearErrors('positiveFiles');
                  }}>
                    <IconX size={16} />
                  </ActionIcon>
                ) : null
              }
              value={positiveFiles}
              rightSectionPointerEvents="auto"
            />
            {positiveFiles && positiveFiles.length > 0 && (
              <Text size="sm" c="dimmed" mt={5} mb={10}>
                {positiveFiles.length} dosya seçildi ({positiveFiles.map(f => f.name).join(', ')})
              </Text>
            )}
            
            <FileInput
              placeholder="Olumsuz Ses dosyalarını seç (Maksimum 5 dosya)"
              label="Olumsuz Ses Dosyaları"
              accept="audio/wav"
              multiple
              error={
                negativeFiles && negativeFiles.length > 5 
                  ? "Maksimum 5 dosya yükleyebilirsiniz" 
                  : tableForm.errors['negativeFiles']
              }
              onChange={async (files) => {
                if (files && files.length > 5) {
                  tableForm.setErrors({ 'negativeFiles': 'Maksimum 5 dosya yükleyebilirsiniz' });
                } else {
                  tableForm.clearErrors('negativeFiles');
                  setNegativeFiles(files || []);
                }
              }}
              rightSection={
                negativeFiles && negativeFiles.length > 0 ? (
                  <ActionIcon color="red" onClick={() => {
                    setNegativeFiles([]);
                    tableForm.clearErrors('negativeFiles');
                  }}>
                    <IconX size={16} />
                  </ActionIcon>
                ) : null
              }
              value={negativeFiles}
              rightSectionPointerEvents="auto"
            />
            {negativeFiles && negativeFiles.length > 0 && (
              <Text size="sm" c="dimmed" mt={5} mb={10}>
                {negativeFiles.length} dosya seçildi ({negativeFiles.map(f => f.name).join(', ')})
              </Text>
            )}
          </>
        )}
      </>
    );
  };

  if (!permissions.includes(channelType + '.PromptTicketView') && !permissions.includes(channelType + '.PromptView')) {
    window.location.href = '/401';
    return <></>;
  }

  return (
    <div className="p-6">
      <Modal
        opened={confirmModal.opened}
        onClose={() => setConfirmModal({ opened: false, action: null, ruleId: null, type: null, ruleName: '' })}
        title={<Text weight={600} size="lg">{confirmModal.action === 'activate' ? 'Kuralı Aktifleştir' : 'Kuralı Deaktifleştir'}</Text>}
        centered
        size="md"
      >
        <Text size="md" mb="md">
          "{confirmModal.ruleName}" adlı kalite kuralını {confirmModal.action === 'activate' ? 'aktifleştirmek' : 'deaktifleştirmek'} istediğinizden emin misiniz?
        </Text>
        <div className="flex justify-end gap-3 mt-4">
          <Button 
            variant="outline" 
            color="gray" 
            onClick={() => setConfirmModal({ opened: false, action: null, ruleId: null, type: null, ruleName: '' })}
            style={{ marginRight: '8px' }}
          >
            İptal
          </Button>
          <Button 
            color={confirmModal.action === 'activate' ? 'green' : 'red'} 
            onClick={handleRuleAction}
          >
            {confirmModal.action === 'activate' ? 'Aktifleştir' : 'Deaktifleştir'}
          </Button>
        </div>
      </Modal>

      <Grid mt={6}>
        {permissions.includes(channelType + '.PromptView') && qualityRulesResponse && (
          <>
            <Grid.Col span={{ md: 6 }}>
              <Card withBorder shadow="md" padding="md" style={{ height: '100%' }}>
                <Text weight={500} size="xl" mb={4} color="blue">
                  Deaktif Kalite Kuralları
                </Text>
                {qualityRulesResponse
                  .map((category) => {
                    const inactiveSubs = category.subCategories.filter((sub) => !sub.isActive);
                    if (inactiveSubs.length) {
                      return {
                        category: category.category,
                        subCategories: inactiveSubs,
                      };
                    }
                    return null;
                  })
                  .filter((category) => category !== null)
                  .map((category, index) => (
                    <div key={index} className="mb-6">
                      <Text weight={600} size="lg" color="teal" className="mb-2">
                        {category.category}
                      </Text>
                      <ul className="space-y-4" style={{ listStyle: 'none', paddingLeft: '0px' }}>
                        {category.subCategories.map((sub, idx) => (
                          <li key={idx} style={{ marginBottom: '8px' }}>
                            {permissions.includes(channelType + '.PromptUpdateActivity') && (
                              <Tooltip label={'Aktifleştir'} withArrow>
                                <ActionIcon
                                  me={'md'}
                                  size="lg"
                                  color="green"
                                  variant="filled"
                                  onClick={() => setConfirmModal({
                                    opened: true,
                                    action: 'activate',
                                    ruleId: sub.id,
                                    type: 'active',
                                    ruleName: sub.subCategory
                                  })}
                                  className="transition-transform transform hover:scale-110 mr-2"
                                >
                                  <IconPlus width={20} height={20} />
                                </ActionIcon>
                              </Tooltip>
                            )}
                            <Tooltip label={sub.description} withArrow>
                              <span className={(sub.isCommon ? 'text-info' : 'text-black') + ' font-medium'}>
                                {sub.subCategory} <b>{sub.points.join(',')}</b>
                              </span>
                            </Tooltip>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
              </Card>
            </Grid.Col>
            <Grid.Col span={{ md: 6 }}>
              <Card withBorder shadow="md" padding="md" style={{ height: '100%' }}>
                <Text weight={500} size="xl" mb={4} color="blue">
                  Aktif Kalite Kuralları
                </Text>
                {qualityRulesResponse
                  .map((category) => {
                    const activeSubs = category.subCategories.filter((sub) => sub.isActive);
                    if (activeSubs.length) {
                      return {
                        category: category.category,
                        subCategories: activeSubs,
                      };
                    }
                    return null;
                  })
                  .filter((category) => category !== null)
                  .map((category, index) => (
                    <div key={index} className="mb-6">
                      <Text weight={600} size="lg" color="teal" className="mb-2">
                        {category.category}
                      </Text>
                      <ul className="space-y-4" style={{ listStyle: 'none', paddingLeft: '0px' }}>
                        {category.subCategories.map((sub, idx) => (
                          <li key={idx} style={{ marginBottom: '8px' }}>
                            {permissions.includes(channelType + '.PromptUpdateActivity') && (
                              <Tooltip label={'Deaktifleştir'} withArrow>
                                <ActionIcon
                                  me={'md'}
                                  size="lg"
                                  color="red"
                                  variant="filled"
                                  onClick={() => setConfirmModal({
                                    opened: true,
                                    action: 'deactivate',
                                    ruleId: sub.id,
                                    type: 'deactive',
                                    ruleName: sub.subCategory
                                  })}
                                  className="transition-transform transform hover:scale-110 mr-2"
                                >
                                  <IconMinus width={20} height={20} />
                                </ActionIcon>
                              </Tooltip>
                            )}
                            <Tooltip label={sub.description} withArrow>
                              <span className={(sub.isCommon ? 'text-info' : 'text-black') + ' font-medium'}>
                                {sub.subCategory} <b>{sub.points.join(',')}</b>
                              </span>
                            </Tooltip>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
              </Card>
            </Grid.Col>
          </>
        )}
        <Grid.Col span={12}>
          <TenantTable
            form={form}
            entityToAdd={async () => {
              return {
                channelType: channelType,
              };
            }}
            onBeforeFormSubmit={(formData) => {
              if (channelType === 'Call') {
                for (let i = 0; i < 5; i++) {
                  formData.delete(`PositiveFiles[${i}]`);
                  formData.delete(`NegativeFiles[${i}]`);
                }
                if (positiveFiles && positiveFiles.length > 0) {
                  positiveFiles.forEach((file, index) => {
                    if (index < 5) {
                      formData.append('PositiveFiles', file);
                    }
                  });
                }
                if (negativeFiles && negativeFiles.length > 0) {
                  negativeFiles.forEach((file, index) => {
                    if (index < 5) {
                      formData.append('NegativeFiles', file);
                    }
                  });
                }
              }
              return formData;
            }}
            urlParameters={'/' + channelType}
            allowAdd={permissions.includes(channelType + '.PromptTicketAdd')}
            allowUpdate={(data) => false}
            allowDelete={false}
            columns={columns}
            entityType="PromptTicket"
            entityText="Ticket Kaydı"
          />
        </Grid.Col>
      </Grid>
    </div>
  );
}
