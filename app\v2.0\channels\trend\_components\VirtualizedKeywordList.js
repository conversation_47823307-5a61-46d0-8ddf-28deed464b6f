'use client';
import React from 'react';
import { FixedSizeList as List } from 'react-window';
import { Card, Text, Badge, Group } from '@mantine/core';

const KeywordItem = ({ index, style, data }) => {
  const { keywords, side } = data;
  const entries = Object.entries(keywords);
  const [keyword, count] = entries[index];

  const getColor = (side) => {
    switch (side) {
      case 'left':
        return 'green';
      case 'right':
        return 'blue';
      default:
        return 'gray';
    }
  };

  return (
    <div style={style}>
      <div style={{ padding: '4px 8px' }}>
        <Card padding="xs" shadow="xs" radius="sm" withBorder>
          <Group position="apart">
            <Text size="sm" weight={500}>
              {keyword}
            </Text>
            <Badge color={getColor(side)} size="sm">
              {count}
            </Badge>
          </Group>
        </Card>
      </div>
    </div>
  );
};

const VirtualizedKeywordList = ({ keywords, side, height = 400, title }) => {
  const keywordEntries = Object.entries(keywords);
  const sortedKeywords = keywordEntries
    .sort(([, a], [, b]) => b - a)
    .reduce((acc, [key, value]) => {
      acc[key] = value;
      return acc;
    }, {});

  if (keywordEntries.length === 0) {
    return (
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Text color="dimmed" ta="center">
          Kelime bulunamadı
        </Text>
      </Card>
    );
  }

  const itemData = {
    keywords: sortedKeywords,
    side,
  };

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      {title && (
        <Text size="lg" weight={600} mb="md">
          {title}
        </Text>
      )}
      <Text size="sm" color="dimmed" mb="sm">
        Toplam {keywordEntries.length} kelime
      </Text>
      <div style={{ height, width: '100%' }}>
        <List height={height} itemCount={keywordEntries.length} itemSize={60} itemData={itemData} overscanCount={5}>
          {KeywordItem}
        </List>
      </div>
    </Card>
  );
};

export default VirtualizedKeywordList;
