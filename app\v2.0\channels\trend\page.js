'use client';
import React from 'react';
import { Button, Card, Grid, Group, Skeleton, Text } from '@mantine/core';
import { IconUser, IconAlertCircle } from '@tabler/icons-react';
import { useEffect, useState, useContext, useMemo } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { useSearchParams } from 'next/navigation';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import SelectFilterPopover from '@/common/components/filters/SelectFilterPopover';
import WordCloud from 'react-d3-cloud';
import VirtualizedKeywordList from './_components/VirtualizedKeywordList';

export default function ChannelTrend() {
  const { fetchAuthClient, permissions } = useContext(AuthContext);
  const searchParams = useSearchParams();
  const channelType = searchParams.get('channelType');

  const [leftDateFilterValue, setLeftDateFilterValue] = useState([null, null]);
  const [leftPersonTypeFilterValue, setLeftPersonTypeFilterValue] = useState(null);
  const [rightDateFilterValue, setRightDateFilterValue] = useState([null, null]);
  const [rightPersonTypeFilterValue, setRightPersonTypeFilterValue] = useState(null);

  const [trendResponse, setTrendResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [agents, setAgents] = useState([]);
  const [formErrors, setFormErrors] = useState({});
  const [tenantParameters, setTenantParameters] = useState(null);

  const KEYWORD_THRESHOLD = 50;

  const fetchAgents = async () => {
    const response = await fetchAuthClient('Agent/' + channelType, {
      method: 'GET',
    });
    var responseJson = await response.json();
    setAgents(responseJson);
  };

  const fetchTenantParameters = async () => {
    const response = await fetchAuthClient('Tenant/parameters/' + channelType, {
      method: 'GET',
    });
    var responseJson = await response.json();
    setTenantParameters(responseJson);
  };

  const setFilter = (value, isEmpty, setFilterMethod) => {
    setFilterMethod(value);
  };

  const validateFilters = () => {
    const errors = {};

    if (!leftDateFilterValue || leftDateFilterValue[0] === null || leftDateFilterValue[1] === null) {
      errors.LeftStartDateTime = ['Sol başlangıç tarihi varsayılan olamaz.'];
      errors.LeftEndDateTime = ['Sol bitiş tarihi varsayılan olamaz.'];
    } else if (leftDateFilterValue[0] >= leftDateFilterValue[1]) {
      if (!errors.LeftStartDateTime) errors.LeftStartDateTime = [];
      errors.LeftStartDateTime.push('Sol bitiş tarihi, sol başlangıç tarihinden büyük olmalıdır.');
    }

    if (!rightDateFilterValue || rightDateFilterValue[0] === null || rightDateFilterValue[1] === null) {
      errors.RightStartDateTime = ['Sağ başlangıç tarihi varsayılan olamaz.'];
      errors.RightEndDateTime = ['Sağ bitiş tarihi varsayılan olamaz.'];
    } else if (rightDateFilterValue[0] >= rightDateFilterValue[1]) {
      if (!errors.RightStartDateTime) errors.RightStartDateTime = [];
      errors.RightStartDateTime.push('Sağ bitiş tarihi, sağ başlangıç tarihinden büyük olmalıdır.');
    }

    if (
      !leftPersonTypeFilterValue ||
      !['Hepsi', 'Sadece Agent', 'Sadece Müşteri'].includes(leftPersonTypeFilterValue)
    ) {
      errors.LeftPersonType = ["Sol kişi tipi sadece 'Sadece Müşteri', 'Sadece Agent' veya 'Hepsi' olabilir."];
    }

    if (
      !rightPersonTypeFilterValue ||
      !['Hepsi', 'Sadece Agent', 'Sadece Müşteri'].includes(rightPersonTypeFilterValue)
    ) {
      errors.RightPersonType = ["Sağ kişi tipi sadece 'Sadece Müşteri', 'Sadece Agent' veya 'Hepsi' olabilir."];
    }

    return errors;
  };

  const handleTrendFormSubmit = async () => {
    const validationErrors = validateFilters();
    if (Object.keys(validationErrors).length > 0) {
      setFormErrors(validationErrors);
      return;
    }

    setFormErrors({});
    setLoading(true);

    let formData = new FormData();
    formData.set('leftStartDateTime', leftDateFilterValue[0]?.toISOString());
    formData.set('leftEndDateTime', leftDateFilterValue[1]?.toISOString());
    formData.set('leftPersonType', leftPersonTypeFilterValue);
    formData.set('rightStartDateTime', rightDateFilterValue[0]?.toISOString());
    formData.set('rightEndDateTime', rightDateFilterValue[1]?.toISOString());
    formData.set('rightPersonType', rightPersonTypeFilterValue);

    const response = await fetchAuthClient(channelType + '/trendAnalysis', { method: 'POST', body: formData });
    if (response.ok) {
      const data = await response.json();
      setTrendResponse(data);
    } else {
      const data = await response.text();
      try {
        var dataJsonObject = JSON.parse(data);
        if (dataJsonObject.errors) {
          setFormErrors(dataJsonObject.errors);
        }
      } catch {
        alert('Hata: ' + data);
      }
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchAgents();
    fetchTenantParameters();
  }, []);

  const getColorForWord = (word, leftKeywords, rightKeywords) => {
    const isLeft = leftKeywords[word.text];
    const isRight = rightKeywords[word.text];

    if (isLeft && isRight) return '#a1a1aa';
    if (isLeft) return '#22c55e';
    if (isRight) return '#3b82f6';
    return '#d1d5db';
  };

  const shouldUseVirtualization = useMemo(() => {
    if (!trendResponse) return false;
    const leftCount = Object.keys(trendResponse.leftKeywordMap || {}).length;
    const rightCount = Object.keys(trendResponse.rightKeywordMap || {}).length;
    return leftCount > KEYWORD_THRESHOLD || rightCount > KEYWORD_THRESHOLD;
  }, [trendResponse]);

  const memoizedLeftContent = useMemo(() => {
    if (!trendResponse) return <></>;

    const leftKeywords = trendResponse.leftKeywordMap;

    if (shouldUseVirtualization) {
      return <VirtualizedKeywordList keywords={leftKeywords} side="left" title="Sol Taraf Kelimeler" height={500} />;
    }

    const rightKeywords = trendResponse.rightKeywordMap;
    return (
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <WordCloud
          font="Arial"
          fontSize={(word) => Math.log2(word.value) * 5}
          spiral="rectangular"
          rotate={() => (Math.random() > 0.5 ? 0 : 90)}
          data={Object.entries(leftKeywords).map(([key, value]) => ({ text: key, value: value }))}
          fill={(word) => getColorForWord(word, leftKeywords, rightKeywords)}
        />
      </Card>
    );
  }, [trendResponse, shouldUseVirtualization]);

  const memoizedRightContent = useMemo(() => {
    if (!trendResponse) return <></>;

    const rightKeywords = trendResponse.rightKeywordMap;

    if (shouldUseVirtualization) {
      return <VirtualizedKeywordList keywords={rightKeywords} side="right" title="Sağ Taraf Kelimeler" height={500} />;
    }

    const leftKeywords = trendResponse.leftKeywordMap;
    return (
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <WordCloud
          font="Arial"
          fontSize={(word) => Math.log2(word.value) * 5}
          spiral="rectangular"
          rotate={() => (Math.random() > 0.5 ? 0 : 90)}
          data={Object.entries(rightKeywords).map(([key, value]) => ({ text: key, value: value }))}
          fill={(word) => getColorForWord(word, leftKeywords, rightKeywords)}
        />
      </Card>
    );
  }, [trendResponse, shouldUseVirtualization]);

  if (!permissions.includes(channelType + '.Trend')) {
    window.location.href = '/401';
    return <></>;
  }
  if (!tenantParameters || !agents) {
    return <></>;
  }

  return (
    <>
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group>
          <DateFilterPopover
            value={leftDateFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setLeftDateFilterValue)}
            label="Sol Tarih Aralığı"
            initDateTypeValue="manuel"
          />
          <SelectFilterPopover
            value={leftPersonTypeFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setLeftPersonTypeFilterValue)}
            icon={<IconUser />}
            label="Sol Kişi Tipi"
            data={[
              { value: 'Hepsi', label: 'Hepsi' },
              { value: 'Sadece Agent', label: 'Sadece Agent' },
              { value: 'Sadece Müşteri', label: 'Sadece Müşteri' },
            ]}
          />
          <DateFilterPopover
            value={rightDateFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setRightDateFilterValue)}
            label="Sağ Tarih Aralığı"
            initDateTypeValue="manuel"
          />
          <SelectFilterPopover
            value={rightPersonTypeFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setRightPersonTypeFilterValue)}
            icon={<IconUser />}
            label="Sağ Kişi Tipi"
            data={[
              { value: 'Hepsi', label: 'Hepsi' },
              { value: 'Sadece Agent', label: 'Sadece Agent' },
              { value: 'Sadece Müşteri', label: 'Sadece Müşteri' },
            ]}
          />
          <Button style={{ marginLeft: 'auto' }} onClick={handleTrendFormSubmit}>
            Filtrele
          </Button>
        </Group>

        {Object.keys(formErrors).length > 0 && (
          <div style={{ marginTop: '10px' }}>
            <Text color="red" size="sm" mb="sm">
              <IconAlertCircle size={16} style={{ marginRight: '5px', verticalAlign: 'middle' }} />
              Tüm gerekli alanları doğru şekilde doldurun.
            </Text>
            {Object.entries(formErrors).map(([field, errors]) => (
              <div key={field} style={{ marginBottom: '5px' }}>
                {errors.map((error, index) => (
                  <small key={index} style={{ color: 'red', display: 'block' }}>
                    {error}
                  </small>
                ))}
              </div>
            ))}
          </div>
        )}
      </Card>
      {loading ? (
        <Skeleton mt="md" style={{ width: '100%', height: '200px' }} />
      ) : (
        <>
          {trendResponse !== null ? (
            <>
              {shouldUseVirtualization && (
                <Text ta="center" mt="md" mb="md" color="blue" size="sm">
                  Çok fazla kelime tespit edildi. Liste görünümü kullanılıyor. (Toplam: Sol{' '}
                  {Object.keys(trendResponse.leftKeywordMap || {}).length}, Sağ{' '}
                  {Object.keys(trendResponse.rightKeywordMap || {}).length})
                </Text>
              )}
              <Grid mt="md">
                <Grid.Col span={{ md: 6 }}>{memoizedLeftContent}</Grid.Col>
                <Grid.Col span={{ md: 6 }}>{memoizedRightContent}</Grid.Col>
              </Grid>
            </>
          ) : null}
        </>
      )}
    </>
  );
}
