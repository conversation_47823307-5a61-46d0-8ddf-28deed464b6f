"use client";
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider,
  NumberInput,
  Pill,
  Rating,
  rem,
  TagsInput,
  Textarea,
} from "@mantine/core";
import { FormError } from "@/common/components/FormError";
import { useForm } from "@mantine/form";
import { handleFormPostSubmit } from "@/common/functions/formFunctions";
import {
  IconMoodCrazyHappy,
  IconMoodCry,
  IconMoodHappy,
  IconMoodSad,
  IconMoodSmile,
} from "@tabler/icons-react";
import { useEffect, useState } from "react";
import { convertToFormData } from "@/common/functions/formFunctions";

const AddAnalysisCommentForm = ({ channelType, id, analysisResultId, fetchAuthClient, permissions, tenantParameters }) => {
  const [comments, setComments] = useState(null);
  const addAnalysisCommentForm = useForm({
    mode: "uncontrolled",
    initialValues: {
      channelType: channelType,
      starCount: 1,
      id: id,
      analysisResultId: analysisResultId
    },
  });
  const fetchComments = async () => {
    const response = await fetchAuthClient("AnalysisComment/" + channelType + "/" + id + "/" + analysisResultId, {
      method: "GET",
    });
    var responseJson = await response.json();
    setComments(responseJson);
  };
  const handleMultipleCallAddFormSubmit = async (e) => {
    handleFormPostSubmit(
      e,
      addAnalysisCommentForm,
      fetchAuthClient("AnalysisComment/add", {
        method: "POST",
        body: convertToFormData(addAnalysisCommentForm.getValues()),
      }),
      async (response) => {
        addAnalysisCommentForm.reset();
        fetchComments();
      },
    );
  };
  const getIconStyle = (color) => ({
    width: rem(24),
    height: rem(24),
    color: color ? `var(--mantine-color-${color}-7)` : undefined,
  });
  if (channelType === "Call") {
    if (!permissions.includes("Call.ViewAnalysisComments")) {
      return <></>;
    }
  }
  else if (channelType === "Chat") {
    if (!permissions.includes("Chat.ViewAnalysisComments")) {
      return <></>;
    }
  }
  else {
    return <></>
  }
  useEffect(() => {
    fetchComments();
  }, []);
  return (
    <>
      {comments ? (
        <>
          <h3>Yorumlar</h3>
          {comments.length === 0 ? (
            <Alert variant="light" color="orange">
              Herhangi bir yorum bulunamadı
            </Alert>
          ) : (
            comments.map((comment, index) => (
              <Card
                key={index}
                shadow="md"
                padding="md"
                radius="md"
                mb="md"
                withBorder
              >
                <Box mb="md" style={{ display: "flex", alignItems: "center" }}>
                  <Box me="md">
                    {comment.userNameSurname +
                      "(" +
                      new Date(comment.date).toLocaleDateString("tr-TR", {
                        hour: "2-digit",
                        minute: "2-digit",
                      }) +
                      ")"}
                  </Box>
                  {(() => {
                    const iconStyle = getIconStyle();
                    switch (comment.starCount) {
                      case 1:
                        return <IconMoodCry style={iconStyle} />;
                      case 2:
                        return <IconMoodSad style={iconStyle} />;
                      case 3:
                        return <IconMoodSmile style={iconStyle} />;
                      case 4:
                        return <IconMoodHappy style={iconStyle} />;
                      case 5:
                        return <IconMoodCrazyHappy style={iconStyle} />;
                      default:
                        return null;
                    }
                  })()}
                </Box>
                {comment.comment}
                {comment.point ? <Badge>{comment.point}</Badge> : <></>}
                <Box mt="md">
                  {comment.tags.map((tag, index) => (
                    <Pill key={index}>{tag}</Pill>
                  ))}
                </Box>
              </Card>
            ))
          )}
        </>
      ) : (
        // comments ?? <Skeleton style={{ width: "100%", height: "300px" }} />
        <h4>Herhangi bir yorum bulunamadı</h4>
      )}
      {((channelType === "Call" && permissions.includes("Call.AddAnalysisComments")) || (channelType === "Chat" && permissions.includes("Chat.AddAnalysisComments"))) && (
        <>
          <Divider mt="md" mb="md" />
          <form onSubmit={handleMultipleCallAddFormSubmit}>
            <Textarea
              {...addAnalysisCommentForm.getInputProps("comment")}
              key={addAnalysisCommentForm.key("comment")}
              withAsterisk
              mb="md"
              label="Analize Yorum"
              rows={5}
              resize="vertical"
            />
            <TagsInput
              withAsterisk
              {...addAnalysisCommentForm.getInputProps("tags")}
              key={addAnalysisCommentForm.key("tags")}
              mb="md"
              label="Tagler"
              data={
                [
                  {
                    group: 'Transkript', items: [
                      'Müşteri - Temsilci Ayrımı Hatalı',
                      'Zaman Kayması',
                      'Konuşmanın Erken Bitmesi',
                      'Değerlendirmede Kullanılan Yasaklı Kelimelerin Aslında Söylenmemiş Olması',
                      'Hatalı Sessizlik Süresi'
                    ]
                  },
                  {
                    group: 'Analiz', items: tenantParameters.qualityRules
                  },
                ]
              }
              acceptValueOnBlur
            />
            <NumberInput
              min={0}
              max={100}
              label="Olması Gereken Puan"
              {...addAnalysisCommentForm.getInputProps("point")}
              key={addAnalysisCommentForm.key("point")}
              mb="md"
            />
            <label>
              Analize Yıldız (1-5) <span style={{ color: "red" }}>*</span>
            </label>
            <Rating
              {...addAnalysisCommentForm.getInputProps("starCount")}
              key={addAnalysisCommentForm.key("starCount")}
              emptySymbol={(value) => {
                const iconStyle = getIconStyle();
                switch (value) {
                  case 1:
                    return <IconMoodCry style={iconStyle} />;
                  case 2:
                    return <IconMoodSad style={iconStyle} />;
                  case 3:
                    return <IconMoodSmile style={iconStyle} />;
                  case 4:
                    return <IconMoodHappy style={iconStyle} />;
                  case 5:
                    return <IconMoodCrazyHappy style={iconStyle} />;
                  default:
                    return null;
                }
              }}
              fullSymbol={(value) => {
                switch (value) {
                  case 1:
                    return <IconMoodCry style={getIconStyle("red")} />;
                  case 2:
                    return <IconMoodSad style={getIconStyle("orange")} />;
                  case 3:
                    return <IconMoodSmile style={getIconStyle("yellow")} />;
                  case 4:
                    return <IconMoodHappy style={getIconStyle("lime")} />;
                  case 5:
                    return <IconMoodCrazyHappy style={getIconStyle("green")} />;
                  default:
                    return null;
                }
              }}
              highlightSelectedOnly
            />
            <Button type="submit" fullWidth mt="md">
              Ekle
            </Button>
            <FormError errorText={addAnalysisCommentForm.errors["formError"]} />
          </form>
        </>
      )}
    </>
  );
};

export default AddAnalysisCommentForm;
