'use client';
import React, { createContext, useState, useEffect, useRef, useCallback } from 'react';
import { convertToFormData } from "@/common/functions/formFunctions";
import LoadingSpinner from '@/common/components/LoadingSpinner';

const API_ENDPOINTS = [
    process.env.API_URL,
    // 'https://webapi1.plukto.com/',
    // 'https://webapi2.plukto.com/',
].filter(Boolean);

let refreshTokenPromise = null;

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [tenant, setTenant] = useState(null);
    const [tenants, setTenants] = useState(null);
    const [permissions, setPermissions] = useState(null);
    const [loading, setLoading] = useState(true);

    const refreshAccessToken = async (apiUrl) => {
        const accessToken = localStorage.getItem("accessToken");
        const refreshToken = localStorage.getItem('refreshToken');
        const refreshResponse = await fetch(
            `${apiUrl}Auth/refreshToken`,
            {
                method: "POST",
                headers: {
                    "x-api-version": process.env.NEXT_PUBLIC_VERSION,
                },
                body: convertToFormData({ refreshToken, accessToken }),
            }
        );
        if (!refreshResponse.ok) {
            throw new Error('Token yenileme işlemi başarısız oldu');
        }
        const data = await refreshResponse.json();
        localStorage.setItem("accessToken", data.accessToken);
        localStorage.setItem("refreshToken", data.refreshToken);
        return data.accessToken;
    };

    const fetchAuthClient = async (url, options = {}) => {
        let apiUrl = process.env.API_URL
        let accessToken = localStorage.getItem('accessToken');
        const fullUrl = apiUrl + url;
        options = options ?? {};
        options.headers = options.headers ?? {};
        options.headers["x-api-version"] = process.env.NEXT_PUBLIC_VERSION;
        if (accessToken) {
            options.headers = {
                ...options.headers,
                Authorization: `Bearer ${accessToken}`,
            };
        }

        let response = await fetch(fullUrl, options);

        if (response.status === 401) {
            if (!refreshTokenPromise) {
                refreshTokenPromise = refreshAccessToken(apiUrl).finally(() => {
                    refreshTokenPromise = null;
                });
            }
            try {
                accessToken = await refreshTokenPromise;
                options.headers["Authorization"] = `Bearer ${accessToken}`;
                response = await fetch(fullUrl, options);
            } catch (err) {
                console.error("Failed to refresh token or retry request:", err);
                localStorage.removeItem("accessToken");
                localStorage.removeItem("refreshToken");
                setUser(null);
                setTenant(null);
                setTenants(null);
                setPermissions(null);
                window.location.href = '/login';
                throw err;
            }
        }
        if (!response.ok && response.status !== 401) {
            let errorMsg = `API request failed: ${response.statusText}`;
            try {
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    errorMsg = data.message || data.error || errorMsg;
                } else if (contentType && contentType.includes('text/plain')) {
                    const text = await response.text();
                    errorMsg = text || errorMsg;
                }
            } catch { }
            throw new Error(errorMsg);
        }

        return response;
    };

    const parseJwt = (token) => {
        const base64Url = token.split(".")[1];
        const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
        const jsonPayload = decodeURIComponent(
            atob(base64)
                .split("")
                .map(function (c) {
                    return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
                })
                .join(""),
        );
        return JSON.parse(jsonPayload);
    }

    useEffect(() => {
        const publicPaths = ["/login", "/logout", "/lcw-kampanya", "/maintenance"];
        const currentPath = window.location.pathname;

        if (publicPaths.includes(currentPath)) {
            setLoading(false);
            return;
        }

        const initializeAuth = async () => {
            setLoading(true);
            try {
                const response = await fetchAuthClient("Auth/me");
                if (!response.ok) {
                    if (response.status === 401) {
                        window.location.href = '/login';
                    } else {
                        console.error("Failed to fetch user data with status:", response.status);
                    }
                    return;
                }
                const data = await response.json();
                const accessToken = localStorage.getItem("accessToken");
                if (!accessToken) {
                    window.location.href = '/login';
                    return;
                }
                const decoded = parseJwt(accessToken);
                setPermissions(decoded.Permission);
                setTenant(decoded.SelectedTenantName);
                setTenants(typeof decoded.Tenant === "string" ? [decoded.Tenant] : decoded.Tenant);
                setUser(data);
            } catch (error) {
                // console.error("Initialization error:", error);
            } finally {
                setLoading(false);
            }
        };

        initializeAuth();
    }, []);

    if (loading) {
        return <LoadingSpinner />;
    }

    return (
        <AuthContext.Provider value={{ user, tenant, tenants, permissions, fetchAuthClient, parseJwt }}>
            {children}
        </AuthContext.Provider>
    );
};
