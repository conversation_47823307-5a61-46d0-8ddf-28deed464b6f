import { Box, Card, Flex, Group, Progress, RingProgress, Stack, Table, Text } from '@mantine/core';
import { IconCircleFilled } from '@tabler/icons-react';
import React from 'react';

const CustomerSatisfaction = ({ dashboardDataCall, dashboardDataChat, filters = [], onItemClick }) => {
  const navigateToSentiment = (sentiment, channelType) => {
    if (onItemClick) {
      onItemClick({
        type: channelType,
        sentiment: sentiment,
      });
    } else {
      const queryParams = new URLSearchParams();
      queryParams.append('channelType', channelType);
      queryParams.append('sentiment', sentiment);
      queryParams.append('fromDashboard', 'true');
      
      filters.forEach((filter) => {
        if (filter.id && filter.value) {
          queryParams.append(filter.id, filter.value);
        }
      });
      
      window.location.href = `/${process.env.VERSION}/channels?${queryParams.toString()}`;
    }
  };

  return (
    <Card withBorder p="md" radius="md" className="h-[63%] 3xl:h-[59%] mt-3">
      <Text c="black" size="xl" tt="uppercase" fw={700} mb="md">
        MÜŞTERİ MEMNUNİYETİ
      </Text>
      <Flex direction={{ base: 'column', md: 'row' }} align="start" gap="xl" justify="space-between">
        <Flex direction="row" gap="xl" align="center" mb="xl" className="w-full">
          <Box className="min-w-[220px]">
            <RingProgress
              className='mt-3'
              size={200}
              thickness={20}
              sections={[
                {
                  value:
                    (((dashboardDataCall.happyCustomerCount ?? 0) + (dashboardDataChat.happyCustomerCount ?? 0)) /
                      dashboardDataCall.analysisCompletedCount) *
                    100,
                  color: 'teal',
                },
                {
                  value:
                    (((dashboardDataCall.unHappyCustomerCount ?? 0) + (dashboardDataChat.unHappyCustomerCount ?? 0)) /
                      dashboardDataCall.analysisCompletedCount) *
                    100,
                  color: 'orange',
                },
              ]}
              label={
                <Text size="xl" ta="center">
                  <b style={{ fontSize: '30px' }}>{dashboardDataCall.analysisCompletedCount.toLocaleString('tr-TR')}</b>
                  <br />
                  Çağrı
                </Text>
              }
            />
          </Box>
          <Box className="w-full" style={{ flex: '1 1 auto' }}>
            <Table striped highlightOnHover className="w-full" style={{ width: '100%', tableLayout: 'fixed' }}> 
              <Table.Thead>
                <Table.Tr>
                  <Table.Th style={{ width: '40%' }}>Duygu Durumu</Table.Th>
                  <Table.Th style={{ textAlign: 'center', width: '30%' }}>Çağrı</Table.Th>
                  <Table.Th style={{ textAlign: 'center', width: '30%' }}>Yazışma</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                <Table.Tr>
                  <Table.Td>
                    <Group spacing="xs">
                      <IconCircleFilled color="teal" size={12} />
                      <Text size="sm">Memnuniyet</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center' }}>
                    <Box
                      style={{
                        cursor: 'pointer',
                        color: 'white',
                        backgroundColor: '#1971c2',
                        padding: '4px 12px',
                        borderRadius: '4px',
                        display: 'inline-block',
                        fontWeight: 600,
                        minWidth: '60px',
                      }}
                      onClick={() => navigateToSentiment('positive', 'Call')}
                    >
                      {(dashboardDataCall.happyCustomerCount ?? 0).toLocaleString('tr-TR')}
                    </Box>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center' }}>
                    <Box
                      style={{
                        cursor: 'pointer',
                        color: 'white',
                        backgroundColor: '#ff9500',
                        padding: '4px 12px',
                        borderRadius: '4px',
                        display: 'inline-block',
                        fontWeight: 600,
                        minWidth: '60px',
                      }}
                      onClick={() => navigateToSentiment('positive', 'Chat')}
                    >
                      {(dashboardDataChat.happyCustomerCount ?? 0).toLocaleString('tr-TR')}
                    </Box>
                  </Table.Td>
                </Table.Tr>
                <Table.Tr>
                  <Table.Td>
                    <Group spacing="xs">
                      <IconCircleFilled color="orange" size={12} />
                      <Text size="sm">Hayal Kırıklığı</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center' }}>
                    <Box
                      style={{
                        cursor: 'pointer',
                        color: 'white',
                        backgroundColor: '#1971c2',
                        padding: '4px 12px',
                        borderRadius: '4px',
                        display: 'inline-block',
                        fontWeight: 600,
                        minWidth: '60px',
                      }}
                      onClick={() => navigateToSentiment('negative', 'Call')}
                    >
                      {(dashboardDataCall.unHappyCustomerCount ?? 0).toLocaleString('tr-TR')}
                    </Box>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center' }}>
                    <Box
                      style={{
                        cursor: 'pointer',
                        color: 'white',
                        backgroundColor: '#ff9500',
                        padding: '4px 12px',
                        borderRadius: '4px',
                        display: 'inline-block',
                        fontWeight: 600,
                        minWidth: '60px',
                      }}
                      onClick={() => navigateToSentiment('negative', 'Chat')}
                    >
                      {(dashboardDataChat.unHappyCustomerCount ?? 0).toLocaleString('tr-TR')}
                    </Box>
                  </Table.Td>
                </Table.Tr>
                <Table.Tr>
                  <Table.Td>
                    <Group spacing="xs">
                      <IconCircleFilled color="#e9ecef" size={12} />
                      <Text size="sm">Nötr</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center' }}>
                    <Box
                      style={{
                        cursor: 'pointer',
                        color: 'white',
                        backgroundColor: '#1971c2',
                        padding: '4px 12px',
                        borderRadius: '4px',
                        display: 'inline-block',
                        fontWeight: 600,
                        minWidth: '60px',
                      }}
                      onClick={() => navigateToSentiment('notr', 'Call')}
                    >
                      {(
                        dashboardDataCall.analysisCompletedCount -
                        ((dashboardDataCall.happyCustomerCount ?? 0) + (dashboardDataCall.unHappyCustomerCount ?? 0))
                      ).toLocaleString('tr-TR')}
                    </Box>
                  </Table.Td>
                  <Table.Td style={{ textAlign: 'center' }}>
                    <Box
                      style={{
                        cursor: 'pointer',
                        color: 'white',
                        backgroundColor: '#ff9500',
                        padding: '4px 12px',
                        borderRadius: '4px',
                        display: 'inline-block',
                        fontWeight: 600,
                        minWidth: '60px',
                      }}
                      onClick={() => navigateToSentiment('notr', 'Chat')}
                    >
                      {/* {(
                        dashboardDataChat.analysisCompletedCount - 
                        ((dashboardDataChat.happyCustomerCount ?? 0) + (dashboardDataChat.unHappyCustomerCount ?? 0))
                      ).toLocaleString('tr-TR')} */}
                      0
                    </Box>
                  </Table.Td>
                </Table.Tr>
              </Table.Tbody>
            </Table>
          </Box>
        </Flex>
      </Flex>
      <Box style={{ width: '100%'}} className="mt-10 3xl:mt-0">
          <Flex justify="space-between">
            <Text>Memnuniyet</Text>
            <Text>
              {(
                (((dashboardDataCall.happyCustomerCount ?? 0) + (dashboardDataChat.happyCustomerCount ?? 0)) /
                  dashboardDataCall.analysisCompletedCount) *
                100
              ).toFixed(2)}
              %
            </Text>
          </Flex>
          <Progress
            color="teal"
            value={
              (((dashboardDataCall.happyCustomerCount ?? 0) + (dashboardDataChat.happyCustomerCount ?? 0)) /
                dashboardDataCall.analysisCompletedCount) *
              100
            }
            size="lg"
            mt={4}
            mb="md"
          />

          <Flex justify="space-between">
            <Text>Hayal Kırıklığı</Text>
            <Text>
              {(
                (((dashboardDataCall.unHappyCustomerCount ?? 0) + (dashboardDataChat.unHappyCustomerCount ?? 0)) /
                  dashboardDataCall.analysisCompletedCount) *
                100
              ).toFixed(2)}
              %
            </Text>
          </Flex>
          <Progress
            color="orange"
            value={
              (((dashboardDataCall.unHappyCustomerCount ?? 0) + (dashboardDataChat.unHappyCustomerCount ?? 0)) /
                dashboardDataCall.analysisCompletedCount) *
              100
            }
            size="lg"
            mt={4}
          />
          
        </Box>
    </Card>
  );
};

export default CustomerSatisfaction;
