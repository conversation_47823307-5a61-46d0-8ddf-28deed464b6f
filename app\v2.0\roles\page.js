'use client';

import { TenantTable } from '@/common/components/TenantTable';
import { AuthContext } from '@/common/contexts/AuthContext';
import { TextInput, Checkbox } from '@mantine/core';
import React, { useContext, useEffect, useState } from 'react';

export default function Roles() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const [allPermissions, setAllPermissions] = useState([]);
  const form = (mode, tableForm) => {
    return (
      <>
        <TextInput
          {...tableForm.getInputProps('name')}
          key={tableForm.key('name')}
          label="İsim"
          withAsterisk
          mt="md"
          mb="md"
        />
        {allPermissions.map((x, xIndex) => (
          <div key={xIndex} style={{ marginBottom: '8px' }}>
            <b>{x.groupName} Yetkileri</b>
            <br />
            <small>{x.groupDescription}</small>
            {x.permissions.map((y, yIndex) => (
              <Checkbox
                key={yIndex}
                mt="md"
                label={y.description}
                checked={selectedPermissions.includes(y.permission)}
                onChange={(event) => {
                  if (event.currentTarget.checked) {
                    setSelectedPermissions((prev) => [...prev, y.permission]);
                  } else {
                    setSelectedPermissions((prev) => prev.filter((perm) => perm !== y.permission));
                  }
                }}
              />
            ))}
          </div>
        ))}
      </>
    );
  };
  const columns = [
    {
      id: 'name',
      accessorKey: 'name',
      header: 'Rol',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{cell.getValue()}</>,
    },
  ];
  const fetchPermissions = async () => {
    const response = await fetchAuthClient('Role/permissions', {
      method: 'GET',
    });
    if (response.ok) {
      var responseJson = await response.json();
      // console.log(responseJson);
      setAllPermissions(responseJson);
    }
  };
  useEffect(() => {
    fetchPermissions();
  }, []);
  if (!permissions.includes('Role.View')) {
    window.location.href = '/401';
    return <></>;
  }
  return (
    <>
      <TenantTable
        allowAdd={permissions.includes('Role.Add')}
        allowUpdate={(data) => data.isDefault === false}
        allowDelete={permissions.includes('Role.Delete')}
        columns={columns}
        entityType="Role"
        entityText="Rol"
        form={form}
        entityToAdd={async () => {
          return {};
        }}
        onBeforeFormSubmit={(formData) => {
          selectedPermissions.forEach((x) => {
            formData.append('permissions', x);
          });
          return formData;
        }}
        entityToUpdate={async (entity) => {
          setSelectedPermissions(entity.permissions);
          return {
            id: entity.id,
            name: entity.name,
            permissions: entity.permissions,
          };
        }}
      />
    </>
  );
}
