'use client';
import React from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { Card, Grid } from '@mantine/core';
import { useContext, useEffect, useState } from 'react';
import DashboardSkeleton from './_components/DashboardSkeleton';
import WordCloudComp from './_components/WordCloudComp';
import AlarmStatus from './_components/AlarmStatus';
import CustomerSatisfaction from './_components/CustomerSatisfaction';
import TopCallReasons from './_components/TopCallReasons';
import WhereWeMakingMistake from './_components/WhereWeMakingMistake';
import TopFiveComplaints from './_components/TopFiveComplaints';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import { getDatesFromType } from '@/common/functions/commonFunctions';
import { useRouter } from 'next/navigation';

export default function Home() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const date = getDatesFromType('lastonemonth');
  const [dateFilterValue, setDateFilterValue] = useState(date);
  const [callDashboardData, setCallDashboardData] = useState(null);
  const [chatDashboardData, setChatDashboardData] = useState(null);
  const [filters, setFilters] = useState([
    {
      id: 'date',
      value: date,
    },
    {
      id: 'isAnalysisCompleted',
      value: 'true',
    },
  ]);
  const router = useRouter();

  const fetchDashboardDatas = async () => {
    setChatDashboardData(null);
    setCallDashboardData(null);
    const [chatResponse, callResponse] = await Promise.all([
      fetchAuthClient(`Chat/dashboard`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      }),
      fetchAuthClient(`Call/dashboard`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      }),
    ]);
    const [chatData, callData] = await Promise.all([chatResponse.json(), callResponse.json()]);
    setChatDashboardData(chatData);
    setCallDashboardData(callData);
  };

  const navigateToChannelsWithFilters = (channelType, additionalParams = []) => {
    const queryParams = new URLSearchParams();
    queryParams.append('channelType', channelType);
    const tempFilters = [...filters, ...additionalParams];
    const tempFiltersJsonString = JSON.stringify(tempFilters);
    const utf8Bytes = new TextEncoder().encode(tempFiltersJsonString);
    const base64String = btoa(String.fromCharCode(...utf8Bytes));
    queryParams.append('filters', base64String);
    router.push(`/${process.env.VERSION}/channels?${queryParams.toString()}`);
  };

  useEffect(() => {
    fetchDashboardDatas();
  }, [filters]);

  if (permissions.includes('Call.View') && permissions.includes('Chat.View')) {
  } else if (permissions.includes('Call.View')) {
    window.location.href = '/' + process.env.VERSION + '/channels?channelType=Call';
    return;
  } else if (permissions.includes('Chat.View')) {
    window.location.href = '/' + process.env.VERSION + '/channels?channelType=Chat';
    return;
  } else {
    window.location.href = '/401';
    return;
  }

  return (
    <>
      <Card shadow="sm" padding="lg" radius="md" withBorder style={{ position: 'sticky', top: 0, zIndex: 2 }}>
        <DateFilterPopover
          value={dateFilterValue}
          onChange={(value, isEmpty) => {
            setDateFilterValue(value);
            setFilters((prevFilters) => {
              const filtered = prevFilters.filter((filter) => filter.id !== 'date');
              if (isEmpty) {
                return [...filtered];
              } else {
                return [
                  ...filtered,
                  {
                    id: 'date',
                    value: value,
                  },
                ];
              }
            });
          }}
          label={'Tarih Aralığı'}
          initDateTypeValue={'lastonemonth'}
        />
      </Card>
      {!callDashboardData || !chatDashboardData ? (
        <DashboardSkeleton />
      ) : (
        <Grid mt="md">
          <Grid.Col span={{ md: 7 }}>
            <WordCloudComp
              dashboardDataCall={callDashboardData}
              dashboardDataChat={chatDashboardData}
              onItemClick={(item) => {
                navigateToChannelsWithFilters(item.type, [
                  {
                    id: 'keywords',
                    value: [item.keywords],
                  },
                ]);
              }}
            />
            <CustomerSatisfaction
              dashboardDataCall={callDashboardData}
              dashboardDataChat={chatDashboardData}
              filters={filters}
              onItemClick={(item) => {
                navigateToChannelsWithFilters(item.type, [
                  {
                    id: 'sentiment',
                    value: [item.sentiment],
                  },
                ]);
              }}
              style={{ marginTop: '1rem' }}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 5 }}>
            <AlarmStatus
              dashboardDataCall={callDashboardData}
              dashboardDataChat={chatDashboardData}
              onItemClick={(item) => {
                navigateToChannelsWithFilters(item.type, [
                  {
                    id: 'alarmCategories',
                    value: [item.alarmCategory],
                  },
                ]);
              }}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 12 }}>
            <Grid>
              <Grid.Col span={{ md: 6 }}>
                <TopCallReasons
                  dashboardDataCall={callDashboardData}
                  dashboardDataChat={chatDashboardData}
                  onItemClick={(item) => {
                    navigateToChannelsWithFilters(item.type, [
                      {
                        id: 'category',
                        value: [item.category],
                      },
                    ]);
                  }}
                />
              </Grid.Col>
              <Grid.Col span={{ md: 6 }}>
                <WhereWeMakingMistake
                  dashboardDataCall={callDashboardData}
                  dashboardDataChat={chatDashboardData}
                  onItemClick={(item) => {
                    navigateToChannelsWithFilters(item.type, [
                      {
                        id: 'analysis',
                        value: [item.analysis],
                      },
                    ]);
                  }}
                />
              </Grid.Col>
            </Grid>
          </Grid.Col>
          <Grid.Col span={{ md: 12 }}>
            <TopFiveComplaints
              dashboardDataCall={callDashboardData}
              dashboardDataChat={chatDashboardData}
              onItemClick={(item) => {
                navigateToChannelsWithFilters(item.type, [
                  {
                    id: 'subCategory',
                    value: [item.subCategory],
                  },
                ]);
              }}
            />
          </Grid.Col>
        </Grid>
      )}
    </>
  );
}
