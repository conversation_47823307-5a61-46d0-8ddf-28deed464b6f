import { Text } from "@mantine/core";
import { IconArrowDownRight, IconArrowUpRight } from "@tabler/icons-react";
import classes from '../../app/v2.0/StatsSegments.module.css';

export   const diffDisplay = ({ currentValue, previousValue }) => {
    const diff = ((currentValue - previousValue) / previousValue) * 100;
    const diffFormatted = `${Math.abs(diff.toFixed(0))}%`;
    if (diff > 0) {
      return (
        <Text c="teal" className={classes.diff} fz="sm" fw={700}>
          <span>{diffFormatted}</span>
          <IconArrowUpRight size={16} style={{ marginBottom: 4 }} stroke={1.5} />
        </Text>
      );
    } else if (diff < 0) {
      return (
        <Text c="red" className={classes.diff} fz="sm" fw={700}>
          <span>{diffFormatted}</span>
          <IconArrowDownRight size={16} style={{ marginBottom: 4 }} stroke={1.5} />
        </Text>
      );
    } else {
      return (
        <Text c="gray" className={classes.diff} fz="sm" fw={700}>
          <span>{diffFormatted}</span>
        </Text>
      );
    }
  };