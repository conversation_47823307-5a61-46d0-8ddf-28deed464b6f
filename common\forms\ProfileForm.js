"use client";
import React from 'react';
import {
  Button,
  TextInput,
  PasswordInput,
  Input,
  InputWrapper,
  Avatar,
  FileInput,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import {
  handleFormPostSubmit,
  handleFetchSubmit,
  convertToFormData
} from "@/common/functions/formFunctions";
import { FormError } from "@/common/components/FormError";
import { IMaskInput } from "react-imask";
import { useContext, useState } from "react";
import { AuthContext } from "@/common/contexts/AuthContext";

const ProfileForm = () => {
  const { user, fetchAuthClient } = useContext(AuthContext);
  const form = useForm({
    mode: "controlled",
    initialValues: {
      name: user.name,
      surname: user.surname,
      phone: user.phone,
    },
  });
  const [file, setFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const handleSubmit = async (e) => {
    let formDatas = convertToFormData(form.getValues());
    if (file) {
      formDatas.append("imageFile", file);
    }
    handleFormPostSubmit(
      e,
      form,
      fetchAuthClient("Auth/me", {
        method: "POST",
        body: formDatas,
      }),
      (data) => {
        window.location.reload();
      },
    );
  };

  const deleteProfileImage = async () => {
    handleFetchSubmit(
      fetchAuthClient("Auth/me/deleteImage", {
        method: "POST",
      }),
      (response) => {
        window.location.reload();
      },
    );
  };

  return (
    <form onSubmit={handleSubmit}>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        {filePreview ? (
          <>
            <Avatar radius="xl" size="xl" src={filePreview} />
            <Button
              onClick={() => {
                setFile(null);
                setFilePreview(null);
              }}
              mt="md"
              color="red"
            >
              Resmi Sil
            </Button>
          </>
        ) : (
          <>
            <FileInput
              mt="md"
              accept="image/*"
              placeholder="Resim seç"
              onChange={(file) => {
                if (file) {
                  setFile(file);
                  setFilePreview(URL.createObjectURL(file));
                }
              }}
            />
            {user.imageFile ? (
              <>
                <Avatar radius="xl" size="xl" mt="md" src={user.imageFile} />
                <Button
                  onClick={() => {
                    deleteProfileImage();
                  }}
                  mt="md"
                  color="red"
                >
                  Mevcut Resmi Sil
                </Button>
              </>
            ) : (
              <></>
            )}
          </>
        )}
      </div>
      <TextInput value={user.email} label="E-Posta" disabled mt="md" />
      <TextInput
        {...form.getInputProps("name")}
        key={form.key("name")}
        label="İsim"
        withAsterisk
        mt="md"
      />
      <TextInput
        {...form.getInputProps("surname")}
        key={form.key("surname")}
        label="Soyisim"
        withAsterisk
        mt="md"
      />
      <InputWrapper label="Telefon">
        <Input
          {...form.getInputProps("phone")}
          key={form.key("phone")}
          mt="md"
          component={IMaskInput}
          mask="000 000 00 00"
        />
      </InputWrapper>
      <PasswordInput
        {...form.getInputProps("password")}
        key={form.key("password")}
        mt="md"
        label="Şifre"
      />
      <Button type="submit" fullWidth mt="md">
        Güncelle
      </Button>
      <FormError errorText={form.errors["formError"]} />
    </form>
  );
};

export default ProfileForm;
