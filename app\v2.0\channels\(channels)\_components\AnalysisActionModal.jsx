import { Box, Button, Checkbox, Divider, Group, Modal, Paper, Radio, ScrollArea, Stack, Text, ThemeIcon, Title } from '@mantine/core';
import { useForm } from '@mantine/form';
import { IconCheck, IconList, IconMinimize, IconRuler, IconServer, IconSettings, IconX } from '@tabler/icons-react';
import React, { useState } from 'react'

const AnalysisActionModal = ({ opened, onClose, selectedIds, qualityRules, channelType, user, tenantName }) => {
  const [loading, setLoading] = useState(false);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  
  const availableTasks = [
    { value: "Category", label: "Kategori" },
    { value: "Insults", label: "Hakaret İçeriği" },
    { value: "Issue_status", label: "Sorun Durumu" },
    { value: "RootCause", label: "<PERSON>ö<PERSON>" },
    { value: "Summary", label: "<PERSON>ze<PERSON>" },
    { value: "Keywords", label: "Anahtar <PERSON>ele<PERSON>" },
    { value: "Information_Category", label: "Bil<PERSON>" },
    { value: "Alarm_Categories", label: "Alarm <PERSON>gorileri" }
  ];
  
  const form = useForm({
    initialValues: {
      tenantName: tenantName || '',
      userId: user?.id || '',
      transcript: true,
      selectedAnalysisRules: qualityRules || [],
      mode: 'Model1',
      listOfTasks: ["Category", "Summary", "Keywords"], 
      forceSilence: false,
      analysisType: 'New'
    },
    validate: {
      selectedAnalysisRules: (value, values) => 
        values.analysisType === 'Exists' && value.length === 0 ? 'En az bir kalite kuralı seçmelisiniz' : null,
      listOfTasks: (value, values) => 
        values.analysisType === 'Exists' && value.length === 0 ? 'En az bir görev seçmelisiniz' : null,
    },
  });

  const handleSubmit = async (values) => {
    setLoading(true);
    // try {
      const basePayload = {
        ids: selectedIds,
        model: values.mode,
        analysisType: values.analysisType
      };
      
      const payload = values.analysisType === 'New' 
        ? basePayload 
        : {
            ...basePayload,
            transcript: values.transcript,
            selectedAnalysisRules: values.selectedAnalysisRules,
            listOfTasks: values.listOfTasks,
            forceSilence: values.forceSilence,
          };
      
      console.log('Payload:', payload);

    //   const response = await fetchAuthClient(channelType + '/analysis', {
    //     method: 'POST',
    //     headers: {
    //       'Content-Type': 'application/json',
    //     },
    //     body: JSON.stringify(payload),
    //   });

    //   if (response.ok) {
    //     onClose();
    //     modals.openConfirmModal({
    //       title: 'İşlem Başarılı',
    //       children: <Text size="sm">Analiz işlemi başlatıldı. İşlem tamamlandığında bilgilendirileceksiniz.</Text>,
    //       labels: { confirm: 'Tamam', cancel: null },
    //       confirmProps: { color: 'green' },
    //     });
    //   } else {
    //     const errorData = await response.json();
    //     form.setErrors({ formError: errorData.message || 'İşlem sırasında bir hata oluştu' });
    //   }
    // } catch (error) {
    //   console.error('Error submitting analysis:', error);
    //   form.setErrors({ formError: 'İşlem sırasında bir hata oluştu' });
    // } finally {
    //   setLoading(false);
    // }
  };

  const renderSimpleViewStatus = () => {
    const isNewAnalysis = form.values.analysisType === 'New';
    
    return (
      <>
        <Box p="sm" bg="gray.0" style={{ borderRadius: '8px' }}>
          <Text size="sm">
            {isNewAnalysis ? (
              "Yeni analiz başlatılacak. Sadece model seçimi yapabilirsiniz."
            ) : (
              <Text span>
                <Text span fw={700}>{form.values.selectedAnalysisRules.length === qualityRules.length 
                  ? "Tüm kalite kuralları" 
                  : `${form.values.selectedAnalysisRules.length} seçili kalite kuralı`}</Text> için analiz işlemi başlatılacak. 
                Kalite kurallarını özelleştirmek veya diğer gelişmiş ayarları yapmak için aşağıdaki butona tıklayın.
              </Text>
            )}
          </Text>
        </Box>
        
        {!isNewAnalysis && (
          <>
            <Group spacing="xs">
              <ThemeIcon size="sm" color={form.values.transcript ? "blue" : "gray"} variant="light" radius="xl">
                {form.values.transcript ? <IconCheck size={14} /> : <IconX size={14} />}
              </ThemeIcon>
              <Text size="sm">{form.values.transcript ? "Transcript Üretilecek" : "Transcript Üretilmeyecek"}</Text>
            </Group>
          </>
        )}
        
        <Group spacing="xs">
          <ThemeIcon size="sm" color="blue" variant="light" radius="xl">
            <IconServer size={14} />
          </ThemeIcon>
          <Text size="sm">Model: {form.values.mode}</Text>
        </Group>
        
        {!isNewAnalysis && (
          <>
            <Group spacing="xs">
              <ThemeIcon 
                size="sm" 
                color={form.values.selectedAnalysisRules.length > 0 ? "blue" : "red"} 
                variant="light" 
                radius="xl"
              >
                <IconList size={14} />
              </ThemeIcon>
              <Text size="sm">
                {form.values.selectedAnalysisRules.length} Kalite Kuralı Seçili 
                {form.values.selectedAnalysisRules.length === 0 && (
                  <Text component="span" size="xs" c="red"> (En az bir kural seçmelisiniz)</Text>
                )}
              </Text>
            </Group>
            
            <Group spacing="xs">
              <ThemeIcon 
                size="sm" 
                color={form.values.listOfTasks.length > 0 ? "blue" : "red"} 
                variant="light" 
                radius="xl"
              >
                <IconList size={14} />
              </ThemeIcon>
              <Text size="sm">
                {form.values.listOfTasks.length} Görev Seçili 
                {form.values.listOfTasks.length === 0 && (
                  <Text component="span" size="xs" c="red"> (En az bir görev seçmelisiniz)</Text>
                )}
              </Text>
            </Group>
            
            <Group spacing="xs">
              <ThemeIcon size="sm" color={form.values.forceSilence ? "blue" : "gray"} variant="light" radius="xl">
                {form.values.forceSilence ? <IconCheck size={14} /> : <IconX size={14} />}
              </ThemeIcon>
              <Text size="sm">{form.values.forceSilence ? "Sessizlik Ölçülecek" : "Sessizlik Ölçülmeyecek"}</Text>
            </Group>
          </>
        )}
      </>
    );
  };

  const isNewAnalysis = form.values.analysisType === 'New';
  const showAdvancedButton = !isNewAnalysis; 
  const formIsInvalid = isNewAnalysis ? false : 
    (form.values.selectedAnalysisRules.length === 0 || form.values.listOfTasks.length === 0);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <ThemeIcon size="md" radius="md" color="blue">
            <IconRuler size={18} />
          </ThemeIcon>
          <Title order={4}>Seçili {channelType === 'Call' ? 'Çağrı' : 'Yazışma'} Analiz İşlemi</Title>
        </Group>
      }
      size={showAdvancedSettings ? "lg" : "md"}
      transitionProps={{ transition: 'slide-down', duration: 300 }}
    >
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack spacing="md">
          <Box bg="blue.0" p="sm" style={{ borderRadius: '8px' }}>
            <Text size="sm" fw={500} c="blue.8">
              Toplam <Text span fw={700}>{selectedIds.length}</Text> adet {channelType === 'Call' ? 'çağrı' : 'yazışma'} için analiz başlatılacak
            </Text>
          </Box>


          <Divider 
            label={
              <Group spacing="xs">
                <IconSettings size={14} />
                <Text size="sm">Analiz Tipi</Text>
              </Group>
            } 
            labelPosition="center" 
          />
          
          <Radio.Group
            name="analysisType"
            label="Analiz Tipi Seçin"
            description="Yeni analiz veya mevcut analizi değiştirme"
            {...form.getInputProps('analysisType')}
          >
            <Group mt="xs">
              <Radio value="New" label="Yeni Analiz (Hiç Analiz Edilmemiş)" />
              <Radio value="Exists" label="Mevcut Analizi Değiştir" />
            </Group>
          </Radio.Group>

          <Radio.Group
            name="mode"
            label="Kullanılacak Model"
            description="Analiz işlemi için kullanılacak modeli seçin"
            {...form.getInputProps('mode')}
          >
            <Group mt="xs">
              <Radio value="Model1" label="Model 1" />
              <Radio value="Model2" label="Model 2" />
            </Group>
          </Radio.Group>

          {showAdvancedSettings && !isNewAnalysis ? (
            <>
              <Divider 
                label={
                  <Group spacing="xs">
                    <IconSettings size={14} />
                    <Text size="sm">Analiz Ayarları</Text>
                  </Group>
                } 
                labelPosition="center" 
              />

              <Checkbox
                label="Transcript (Konuşma Metni) Üret"
                description="İşaretlenirse analiz işlemi sırasında konuşma metni üretilir"
                {...form.getInputProps('transcript', { type: 'checkbox' })}
              />
              
              <Checkbox
                label="Sessizliği Ölç"
                description="İşaretlenirse analiz işlemi sırasında sessizlik ölçülür"
                {...form.getInputProps('forceSilence', { type: 'checkbox' })}
              />

              <Divider 
                label={
                  <Group spacing="xs">
                    <IconList size={14} />
                    <Text size="sm">Kalite Kuralları</Text>
                  </Group>
                } 
                labelPosition="center" 
              />
              
              <Group position="apart">
                <Text size="sm" c="dimmed">
                  Analiz edilmesini istediğiniz kalite kurallarını seçin
                </Text>
                <Checkbox
                  label="Tümünü Seç/Kaldır"
                  checked={form.values.selectedAnalysisRules.length === qualityRules.length}
                  indeterminate={form.values.selectedAnalysisRules.length > 0 && form.values.selectedAnalysisRules.length < qualityRules.length}
                  onChange={(event) => {
                    const checked = event.currentTarget.checked;
                    form.setFieldValue(
                      'selectedAnalysisRules',
                      checked ? qualityRules : []
                    );
                  }}
                  size="xs"
                />
              </Group>

              <ScrollArea h={200} offsetScrollbars scrollbarSize={6}>
                <Paper p="xs" withBorder>
                  <Stack spacing="xs">
                    {qualityRules.map((rule) => (
                      <Checkbox
                        key={rule}
                        label={rule}
                        checked={form.values.selectedAnalysisRules.includes(rule)}
                        onChange={(event) => {
                          const checked = event.currentTarget.checked;
                          form.setFieldValue(
                            'selectedAnalysisRules',
                            checked
                              ? [...form.values.selectedAnalysisRules, rule]
                              : form.values.selectedAnalysisRules.filter((item) => item !== rule)
                          );
                        }}
                      />
                    ))}
                  </Stack>
                </Paper>
              </ScrollArea>
              
              <Divider 
                label={
                  <Group spacing="xs">
                    <IconList size={14} />
                    <Text size="sm">Görevler</Text>
                  </Group>
                } 
                labelPosition="center" 
              />
              
              <Group position="apart">
                <Text size="sm" c="dimmed">
                  Uygulanmasını istediğiniz görevleri seçin
                </Text>
                <Checkbox
                  label="Tümünü Seç/Kaldır"
                  checked={form.values.listOfTasks.length === availableTasks.length}
                  indeterminate={form.values.listOfTasks.length > 0 && form.values.listOfTasks.length < availableTasks.length}
                  onChange={(event) => {
                    const checked = event.currentTarget.checked;
                    form.setFieldValue(
                      'listOfTasks',
                      checked ? availableTasks.map(task => task.value) : []
                    );
                  }}
                  size="xs"
                />
              </Group>

              <ScrollArea h={150} offsetScrollbars scrollbarSize={6}>
                <Paper p="xs" withBorder>
                  <Stack spacing="xs">
                    {availableTasks.map((task) => (
                      <Checkbox
                        key={task.value}
                        label={task.label}
                        checked={form.values.listOfTasks.includes(task.value)}
                        onChange={(event) => {
                          const checked = event.currentTarget.checked;
                          form.setFieldValue(
                            'listOfTasks',
                            checked
                              ? [...form.values.listOfTasks, task.value]
                              : form.values.listOfTasks.filter((item) => item !== task.value)
                          );
                        }}
                      />
                    ))}
                  </Stack>
                </Paper>
              </ScrollArea>
            </>
          ) : (
            !isNewAnalysis && renderSimpleViewStatus()
          )}

          {isNewAnalysis && renderSimpleViewStatus()}

          {form.errors.formError && (
            <Text color="red" size="sm">
              {form.errors.formError}
            </Text>
          )}

          <Group position="apart" mt="md">
            {showAdvancedButton && (
              <Button 
                variant="subtle" 
                color="gray"
                leftSection={showAdvancedSettings ? <IconMinimize size={16} /> : <IconSettings size={16} />}
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
              >
                {showAdvancedSettings ? "Basit Görünüme Dön" : "Gelişmiş Ayarlar"}
              </Button>
            )}

            {!showAdvancedButton && <Box />}
            
            <Group spacing="sm">
              <Button variant="light" onClick={onClose}>İptal</Button>
              <Button 
                type="submit" 
                loading={loading}
                disabled={formIsInvalid}
              >
                Analiz Başlat
              </Button>
            </Group>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
};

export default AnalysisActionModal