import React from 'react';
import { <PERSON>ge, Tooltip, HoverCard, Box, CopyButton, ActionIcon } from '@mantine/core';
import { IconCheck, IconCopy } from '@tabler/icons-react';

export const getChannelTableColumns = (channelType, tenantParameters, agents, label) => {
  const columns = [
    {
      id: 'provider',
      accessorKey: 'provider',
      header: 'Sağlayıcı',
      enableSorting: false,
      enableColumnFilter: false,
      adminOnly: true,
      Cell: ({ cell, row }) => {
        return (
          <Badge size="sm" color="gray">
            {row.original.provider}
          </Badge>
        );
      },
    },
    {
      id: 'date',
      accessorKey: 'date',
      header: 'Tarih',
      enableSorting: true,
      enableColumnFilter: false,
      Cell: ({ cell }) => (
        <>
          {new Date(cell.getValue()).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          })}
        </>
      ),
    },
    {
      id: 'agentId',
      accessorKey: 'agentId',
      header: 'Temsilci',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        let cellValue = cell.getValue();
        if (!cellValue) {
          return <></>;
        }
        let agent = agents.find((x) => x.id === cellValue);
        if (!agent) {
          return <></>;
        }
        return <>{agent.name + ' ' + agent.surname}</>;
      },
    },
    {
      id: 'identifier',
      accessorKey: 'identifier',
      header: 'ID',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        return (
          <>
            <CopyButton value={cell.getValue()} timeout={2000}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? 'Kopyalandı' : 'Kopyala'} withArrow position="right">
                  <ActionIcon
                    color={copied ? 'teal' : 'gray'}
                    variant="subtle"
                    onClick={(e) => {
                      e.stopPropagation();
                      copy();
                    }}
                    className="copy-button"
                  >
                    {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
            {cell.getValue()}
          </>
        );
      },
    },
    {
      id: 'language',
      accessorKey: 'language',
      header: 'Dil',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        return <img src={'https://flagsapi.com/' + cell.getValue().toUpperCase() + '/flat/32.png'} />;
      },
    },
    {
      id: 'issueStatus',
      accessorKey: 'issueStatus',
      header: 'Sorun Durumu',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        if (row.original.info && row.original.info.issueStatus) {
          return (
            <Badge
              size="sm"
              color={
                row.original.info.issueStatus === 'Bilgi Talebi'
                  ? 'gray'
                  : row.original.info.issueStatus === 'Müşterinin Problemi Vardı, Çözüldü'
                  ? 'teal'
                  : 'red'
              }
            >
              {row.original.info.issueStatus}
            </Badge>
          );
        } else {
          return <></>;
        }
      },
    },
    {
      id: 'category',
      accessorKey: 'category',
      header: 'Ana Kategori',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        if (row.original.info) {
          return (
            <Tooltip label={row.original.info.category} withinPortal>
              <Badge size="sm" color="gray">
                {row.original.info.category}
              </Badge>
            </Tooltip>
          );
        } else {
          return <></>;
        }
      },
    },
    {
      id: 'subCategory',
      accessorKey: 'subCategory',
      header: 'Alt Kategori',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        if (row.original.info) {
          return (
            <Tooltip label={row.original.info.subCategory} withinPortal>
              <Badge size="sm" color="gray">
                {row.original.info.subCategory}
              </Badge>
            </Tooltip>
          );
        } else {
          return <></>;
        }
      },
    },
    {
      id: 'point',
      accessorKey: 'point',
      header: 'Kalite Puanı',
      enableSorting: true,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        if (row.original.info) {
          if (row.original.info.point >= tenantParameters.targetedQualityPoint) {
            return (
              <Badge size="sm" color="green">
                {row.original.info.point}
              </Badge>
            );
          } else if (row.original.info.point >= 85 && row.original.info.point < tenantParameters.targetedQualityPoint) {
            return (
              <Badge size="sm" color="orange">
                {row.original.info.point}
              </Badge>
            );
          } else {
            return (
              <Badge size="sm" color="red">
                {row.original.info.point}
              </Badge>
            );
          }
        } else {
          return <></>;
        }
      },
    },
    {
      id: 'duration',
      accessorKey: 'duration',
      header: 'Süre',
      enableSorting: true,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        return <>{row.original.duration} sn</>;
      },
    },
    {
      id: 'maxSlience',
      accessorKey: 'maxSlience',
      header: 'Sessizlik',
      enableSorting: true,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        if (row.original.info) {
          return <>{row.original.info.maxSlience} sn</>;
        } else {
          return <></>;
        }
      },
    },
    {
      id: 'analysis',
      accessorKey: 'analysis',
      header: 'Kalite Kural Hatası',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        if (row.original.info) {
          if (row.original.info.analysis) {
            const failedAnalysisResults = Object.entries(row.original.info.analysis)
              .filter(([key, value]) => value.isAgentHaveWrongAction === true)
              .map(([key]) => key);
            if (failedAnalysisResults.length > 0) {
              return (
                <HoverCard width={280} shadow="md">
                  <HoverCard.Target>
                    <Badge color="red">{failedAnalysisResults.length} Hata</Badge>
                  </HoverCard.Target>
                  <HoverCard.Dropdown>
                    <Box style={{ display: 'flex', flexDirection: 'column' }}>
                      {failedAnalysisResults.length > 0 ? (
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                          {failedAnalysisResults.map((prompt, index) => (
                            <Badge size="sm" style={{ backgroundColor: 'red', color: 'white' }} key={index}>
                              {prompt}
                            </Badge>
                          ))}
                        </div>
                      ) : (
                        <></>
                      )}
                    </Box>
                  </HoverCard.Dropdown>
                </HoverCard>
              );
            } else {
              return <Badge color="teal">0 Hata</Badge>;
            }
          } else {
            return (
              <Badge color="gray" size="sm">
                Analiz Sonucu Yok
              </Badge>
            );
          }
        } else {
          return (
            <Badge color="gray" size="sm">
              Analiz Sonucu Yok
            </Badge>
          );
        }
      },
    },
    {
      id: 'agentBadLanguage',
      accessorKey: 'agentBadLanguage',
      header: 'Küfür/Hakaret',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        const swears = row.original.info && row.original.info.agentSwears ? row.original.info.agentSwears : [];
        const insults = row.original.info && row.original.info.agentInsults ? row.original.info.agentInsults : [];
        const allBadLanguage = [...swears, ...insults];

        if (allBadLanguage.length === 0) {
          return (
            <Badge color="teal" size="sm">
              Temiz
            </Badge>
          );
        } else if (allBadLanguage.length === 1) {
          return (
            <Badge color="red" size="sm">
              {allBadLanguage[0]}
            </Badge>
          );
        } else {
          return (
            <HoverCard width={280} shadow="md">
              <HoverCard.Target>
                <Badge color="red">
                  {swears.length} Küfür, {insults.length} Hakaret
                </Badge>
              </HoverCard.Target>
              <HoverCard.Dropdown>
                <Box style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                  {swears.length > 0 && (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                      <Text size="xs" fw={500} c="red">
                        Küfürler:
                      </Text>
                      {swears.map((swear, index) => (
                        <Badge size="sm" color="red" key={`swear-${index}`}>
                          {swear}
                        </Badge>
                      ))}
                    </div>
                  )}
                  {insults.length > 0 && (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                      <Text size="xs" fw={500} c="red">
                        Hakaretler:
                      </Text>
                      {insults.map((insult, index) => (
                        <Badge size="sm" color="red" key={`insult-${index}`}>
                          {insult}
                        </Badge>
                      ))}
                    </div>
                  )}
                </Box>
              </HoverCard.Dropdown>
            </HoverCard>
          );
        }
      },
    },
    {
      id: 'type',
      accessorKey: 'type',
      header: 'Kayıt Tipi',
      enableSorting: false,
      enableColumnFilter: false,
      adminOnly: true,
      Cell: ({ cell, row }) => {
        return (
          <Badge size="sm" color="gray">
            {row.original.type}
          </Badge>
        );
      },
    },
    {
      id: 'caller',
      accessorKey: 'caller',
      header: 'Arayan',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        return (
          <Badge size="sm" color="gray">
            {row.original.caller}
          </Badge>
        );
      },
    },
    {
      id: 'called',
      accessorKey: 'called',
      header: 'Aranan',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        return (
          <Badge size="sm" color="gray">
            {row.original.called}
          </Badge>
        );
      },
    },
    {
      id: 'callDirection',
      accessorKey: 'callDirection',
      header: 'Ekip',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        return (
          <Badge size="sm" color="gray">
            {row.original.communicationType}
          </Badge>
        );
      },
    },
  ];
  return columns;
};
