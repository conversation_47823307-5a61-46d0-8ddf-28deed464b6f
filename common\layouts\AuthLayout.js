"use client";
import React from 'react';
import { MantineProvider, createTheme } from "@mantine/core";
import { ModalsProvider } from "@mantine/modals";
import { Notifications } from "@mantine/notifications";
import { AuthProvider } from "@/common/contexts/AuthContext";
import { DatesProvider } from "@mantine/dates";
import "dayjs/locale/tr";

const theme = createTheme({
    colorScheme: "light",
    primaryColor: "teal",
    primaryShade: { light: 6, dark: 8 },
    fontFamily: "Inter, sans-serif",
    headings: {
        fontFamily: "Poppins, sans-serif",
        fontWeight: 700,
        sizes: {
            h1: { fontSize: "2.5rem", lineHeight: 1.2 },
            h2: { fontSize: "2rem", lineHeight: 1.3 },
            h3: { fontSize: "1.75rem", lineHeight: 1.4 },
        },
    },
    components: {
        Button: {
            styles: (theme) => ({
                root: {
                    borderRadius: theme.radius.md,
                    boxShadow: theme.shadows.sm,
                    "&:hover": {
                        backgroundColor: theme.colors.teal[7],
                    },
                },
            }),
        },
        Card: {
            styles: (theme) => ({
                root: {
                    borderRadius: theme.radius.md,
                    boxShadow: theme.shadows.md,
                    backgroundColor: theme.colorScheme === "dark" ? theme.colors.dark[7] : theme.white,
                },
            }),
        },
    },
});

export function AuthLayout({ children }) {
    return (
        <MantineProvider theme={theme} withGlobalStyles withNormalizeCSS>
            <ModalsProvider>
                <Notifications />
                <AuthProvider>
                    <DatesProvider settings={{ locale: "tr" }}>
                        {children}
                    </DatesProvider>
                </AuthProvider>
            </ModalsProvider>
        </MantineProvider>
    );
}
