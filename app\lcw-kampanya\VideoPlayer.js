"use client";
import React, { useRef } from 'react';
import { Button, Typography, Box, Paper } from '@mui/material'; 
import './VideoPlayer.css'; 

const VideoPlayer = () => {
  const videoRef = useRef(null);

  return (
    <Box 
      className="video-container" 
      display="flex" 
      flexDirection="column" 
      alignItems="center" 
      justifyContent="center"
      minHeight="100vh"
      bgcolor="#f4f4f4"
    >
      <Typography variant="h4" className="video-title" gutterBottom>
        LC WAIKIKI
      </Typography>

      <Paper elevation={3} className="video-player-wrapper">
        <video ref={videoRef} className="video-player" controls>
          <source src="/videos/lcwAd.mp4" type="video/mp4" />
          Web tarayıcınız videoyu oynatmıyor.
        </video>
      </Paper>

      <Box mt={2}>
      </Box>
    </Box>
  );
};

export default VideoPlayer;
