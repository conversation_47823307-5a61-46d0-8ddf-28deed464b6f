export const getComparisonText = (activePeriod) => {
  switch (activePeriod) {
    case 'TODAY':
      return 'Bugü<PERSON><PERSON>n başlangıcından itibaren kıyaslanmıştır.';
    case '1DAY':
      return '1 gün öncesine göre kıyaslanmıştır.';
    case '1WEEK':
      return '1 hafta öncesine göre kıyaslanmıştır.';
    case '1MONTH':
      return '1 ay öncesine göre kıyaslanmıştır.';
    case '6MONTHS':
      return '6 ay öncesine göre kıyaslanmıştır.';
    case '1YEAR':
      return '1 yıl öncesine göre kıyaslanmıştır.';
    default:
      return '1 ay öncesine göre kıyaslanmıştır.';
  }
};
