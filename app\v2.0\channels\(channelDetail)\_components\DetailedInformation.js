'use client';
import React from 'react';
import { Badge, CopyButton, Grid, Tooltip, ActionIcon } from '@mantine/core';
import { IconCheck, IconCopy } from '@tabler/icons-react';

const getSentimentEmoji = (sentimentScore) => {
  if (sentimentScore === null || sentimentScore === undefined) return '';
  const score = Math.round(sentimentScore);
  if (score < 50) return '😞';
  if (score === 50) return '😐';
  if (score > 50 && score <= 70) return '😊';
  if (score > 70) return '😄';
  return '';
};

export default function DetailedInformation({
  detail,
  channelType,
  agents,
  tenantParameters,
  setSearchTerm,
  agentPercentage,
  customerPercentage,
  formatPercentage,
}) {
  return (
    <Grid>
      {detail.dto.info && (
        <Grid.Col span={{ md: 6 }} className=" max-h-[32rem] overflow-y-auto">
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Maksimum Sessizlik:</h6>
            <Badge size="sm" color="gray">
              {detail.dto.info.maxSlience} Saniye
            </Badge>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Kategori:</h6>
            <Badge size="sm" color="gray">
              {detail.dto.info.category}
            </Badge>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Alt Kategori:</h6>
            <Badge size="sm" color="gray">
              {detail.dto.info.subCategory}
            </Badge>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Sorun Durumu:</h6>
            <Badge
              size="sm"
              color={
                detail.dto.info.issueStatus === 'Bilgi Talebi'
                  ? 'gray'
                  : detail.dto.info.issueStatus === 'Müşterinin Problemi Vardı, Çözüldü'
                  ? 'teal'
                  : 'red'
              }
            >
              {detail.dto.info.issueStatus}
            </Badge>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Kalite Puanı:</h6>
            {(() => {
              if (detail.dto.info.point >= tenantParameters.targetedQualityPoint) {
                return (
                  <Badge size="sm" color="green">
                    {detail.dto.info.point}
                  </Badge>
                );
              } else if (detail.dto.info.point >= 85 && detail.dto.info.point < tenantParameters.targetedQualityPoint) {
                return (
                  <Badge size="sm" color="orange">
                    {detail.dto.info.point}
                  </Badge>
                );
              } else {
                return (
                  <Badge size="sm" color="red">
                    {detail.dto.info.point}
                  </Badge>
                );
              }
            })()}
          </div>
          {detail.dto.info.agentSentiment !== null && detail.dto.info.agentSentiment !== undefined && (
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
              <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Temsilci Duygu Durumu:</h6>
              <Badge size="sm" color="gray" style={{ marginRight: '4px' }}>
                {detail.dto.info.agentSentiment.toFixed(1)}%
              </Badge>
              <span style={{ fontSize: '1.2em' }}>{getSentimentEmoji(detail.dto.info.agentSentiment)}</span>
            </div>
          )}
          {detail.dto.info.customerSentiment !== null && detail.dto.info.customerSentiment !== undefined && (
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
              <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Müşteri Duygu Durumu:</h6>
              <Badge size="sm" color="gray" style={{ marginRight: '4px' }}>
                {detail.dto.info.customerSentiment.toFixed(1)}%
              </Badge>
              <span style={{ fontSize: '1.2em' }}>{getSentimentEmoji(detail.dto.info.customerSentiment)}</span>
            </div>
          )}
          {detail.dto.info.agentInsults && detail.dto.info.agentInsults.length > 0 ? (
            <>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                <h6
                  className="mb-0 fs-14 flex-grow-1"
                  style={{ fontWeight: 'bold', color: '#4A4A4A', marginRight: 'auto' }}
                >
                  Temsilci Hakaretler:
                </h6>
                {detail.dto.info.agentInsults.map((a, b) => (
                  <Badge
                    size="sm"
                    color="red"
                    className="cursor-pointer"
                    key={b}
                    onClick={() => {
                      setSearchTerm(a);
                    }}
                  >
                    {a}
                  </Badge>
                ))}
              </div>
            </>
          ) : (
            <></>
          )}
          {detail.dto.info.agentSwears && detail.dto.info.agentSwears.length > 0 ? (
            <>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                <h6
                  className="mb-0 fs-14 flex-grow-1"
                  style={{ fontWeight: 'bold', color: '#4A4A4A', marginRight: 'auto' }}
                >
                  Temsilci Küfürler:
                </h6>
                {detail.dto.info.agentSwears.map((a, b) => (
                  <Badge
                    size="sm"
                    color="red"
                    className="cursor-pointer"
                    key={b}
                    onClick={() => {
                      setSearchTerm(a);
                    }}
                  >
                    {a}
                  </Badge>
                ))}
              </div>
            </>
          ) : (
            <></>
          )}
          {detail.dto.info.customerInsults && detail.dto.info.customerInsults.length > 0 ? (
            <>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                <h6
                  className="mb-0 fs-14 flex-grow-1"
                  style={{ fontWeight: 'bold', color: '#4A4A4A', marginRight: 'auto' }}
                >
                  Müşteri Hakaretler:
                </h6>
                {detail.dto.info.customerInsults.map((a, b) => (
                  <Badge
                    size="sm"
                    color="red"
                    className="cursor-pointer"
                    key={b}
                    onClick={() => {
                      setSearchTerm(a);
                    }}
                  >
                    {a}
                  </Badge>
                ))}
              </div>
            </>
          ) : (
            <></>
          )}
          {detail.dto.info.customerSwears && detail.dto.info.customerSwears.length > 0 ? (
            <>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                <h6
                  className="mb-0 fs-14 flex-grow-1"
                  style={{ fontWeight: 'bold', color: '#4A4A4A', marginRight: 'auto' }}
                >
                  Müşteri Küfürler:
                </h6>
                {detail.dto.info.customerSwears.map((a, b) => (
                  <Badge
                    size="sm"
                    color="red"
                    className="cursor-pointer"
                    key={b}
                    onClick={() => {
                      setSearchTerm(a);
                    }}
                  >
                    {a}
                  </Badge>
                ))}
              </div>
            </>
          ) : (
            <></>
          )}
          {channelType === 'Call' && agentPercentage !== null && customerPercentage !== null && (
            <div style={{ display: 'flex', flexDirection: 'column', marginBottom: '16px' }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                <span style={{ fontWeight: 'bold', marginRight: 'auto' }}>Temsilci Konuşma Oranı:</span>
                <Badge size="sm" color="blue">
                  {formatPercentage(agentPercentage)}
                </Badge>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '7px' }}>
                <span style={{ fontWeight: 'bold', marginRight: 'auto' }}>Müşteri Konuşma Oranı:</span>
                <Badge size="sm" color="green">
                  {formatPercentage(customerPercentage)}
                </Badge>
              </div>
            </div>
          )}
          {detail.dto.info.keywords &&
          detail.dto.info.keywords.keywords &&
          detail.dto.info.keywords.keywords.length > 0 ? (
            <>
              <div
                style={{ display: 'flex', alignItems: 'center', marginBottom: '16px', flexWrap: 'wrap', gap: '4px' }}
              >
                <h6
                  className="mb-0 fs-14 flex-grow-1"
                  style={{ fontWeight: 'bold', color: '#4A4A4A', marginRight: 'auto', width: '100%' }}
                >
                  Anahtar Kelimeler:
                </h6>
                {detail.dto.info.keywords.keywords.map((keyword, index) => (
                  <Badge
                    size="sm"
                    color="blue"
                    className="cursor-pointer"
                    key={index}
                    onClick={() => {
                      setSearchTerm(keyword);
                    }}
                  >
                    {keyword}
                  </Badge>
                ))}
              </div>
            </>
          ) : (
            <></>
          )}
          <div style={{ marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Kalite AI Yorumu:</h6>
            <p style={{ textAlign: 'justify', margin: '0px' }}>{detail.dto.info.aiQualityFeedback}</p>
          </div>
          <div style={{ marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Özet:</h6>
            <p style={{ textAlign: 'justify', margin: '0px' }}>{detail.dto.info.summary}</p>
          </div>
          {detail.dto.info.rootCause && (
            <>
              <div style={{ marginBottom: '16px' }}>
                <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Ana Kök Neden:</h6>
                <p style={{ textAlign: 'justify', margin: '0px' }}>{detail.dto.info.rootCause.ana_kok_neden}</p>
              </div>
              <div style={{ marginBottom: '16px' }}>
                <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Alt Kök Neden:</h6>
                <p style={{ textAlign: 'justify', margin: '0px' }}>{detail.dto.info.rootCause.alt_kok_neden}</p>
              </div>
              {/* <div style={{ marginBottom: '16px' }}>
                <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>
                  Ana Kök Neden Problem Kaynağı:
                </h6>
                <p style={{ textAlign: 'justify', margin: '0px' }}>
                  {detail.dto.info.rootCause.ana_kok_neden_problem_kaynagi}
                </p>
              </div> */}
              <div style={{ marginBottom: '16px' }}>
                <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>
                  Ana Kök Neden Problem Özeti:
                </h6>
                <p style={{ textAlign: 'justify', margin: '0px' }}>
                  {detail.dto.info.rootCause.ana_kok_neden_problem_ozeti}
                </p>
              </div>
              <div style={{ marginBottom: '16px' }}>
                <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Çözüm Önerisi:</h6>
                <p style={{ textAlign: 'justify', margin: '0px' }}>
                  {detail.dto.info.rootCause.ne_olsaydi_cagri_merkezini_ulasmazdi}
                </p>
              </div>
            </>
          )}
        </Grid.Col>
      )}
      <Grid.Col span={{ md: 6 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Id:</h6>
          <Badge size="sm" color="gray">
            {detail.dto.identifier}
          </Badge>
          <CopyButton value={detail.dto.identifier} timeout={2000}>
            {({ copied, copy }) => (
              <Tooltip label={copied ? 'Kopyalandı' : 'Kopyala'} withArrow position="right">
                <ActionIcon color={copied ? 'teal' : 'gray'} variant="subtle" onClick={copy}>
                  {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                </ActionIcon>
              </Tooltip>
            )}
          </CopyButton>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Tarih:</h6>
          <Badge size="sm" color="gray">
            {new Date(detail.dto.date).toLocaleDateString('tr-TR', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
            })}
          </Badge>
        </div>
        {channelType === 'Chat' && (
          <>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
              <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>
                Temsilci Sohbete Katılma Tarihi:
              </h6>
              <Badge size="sm" color="gray">
                {new Date(detail.dto.agentJoinDate).toLocaleDateString('tr-TR', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                })}
              </Badge>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
              <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>
                Temsilci İlk Mesaj Gönderim Tarihi:
              </h6>
              <Badge size="sm" color="gray">
                {new Date(detail.dto.agentFirstMessageDate).toLocaleDateString('tr-TR', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                })}
              </Badge>
            </div>
          </>
        )}
        {channelType === 'Call' && (
          <>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
              <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Kayıt Tipi:</h6>
              <Badge size="sm" color="gray">
                {detail.dto.type}
              </Badge>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
              <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Arayan:</h6>
              <Badge size="sm" color="gray">
                {detail.dto.caller}
              </Badge>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
              <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Aranan:</h6>
              <Badge size="sm" color="gray">
                {detail.dto.called}
              </Badge>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
              <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Yön:</h6>
              <Badge size="sm" color="gray">
                {detail.dto.communicationType}
              </Badge>
            </div>
          </>
        )}
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Sağlayıcı:</h6>
          <Badge size="sm" color="gray">
            {detail.dto.provider}
          </Badge>
        </div>
        {channelType === 'Chat' && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Kanal Tipi:</h6>
            <Badge size="sm" color="gray">
              {detail.dto.channelType}
            </Badge>
          </div>
        )}
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Süre:</h6>
          <Badge size="sm" color="gray">
            {detail.dto.duration} Saniye
          </Badge>
        </div>
        {detail.dto.agentId && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Temsilci:</h6>
            {(() => {
              let agent = agents.find((x) => x.id === detail.dto.agentId);
              if (!agent) {
                return <></>;
              }
              return (
                <Badge size="sm" color="gray">
                  {agent.name + ' ' + agent.surname}
                </Badge>
              );
            })()}
          </div>
        )}
        {detail.dto.language && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <h6 style={{ fontWeight: 'bold', marginBottom: '0px', marginRight: 'auto' }}>Dil:</h6>
            <img
              src={'https://flagsapi.com/' + detail.dto.language.toUpperCase() + '/flat/32.png'}
              alt={detail.dto.language}
            />
          </div>
        )}
      </Grid.Col>
    </Grid>
  );
}
