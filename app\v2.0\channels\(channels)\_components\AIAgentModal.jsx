import React from 'react';
import { Modal, ThemeIcon, Text, Group, Box } from '@mantine/core';
import { IconRobot } from '@tabler/icons-react';
import { MarkdownTypewriter } from 'react-markdown-typewriter';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';

const AIAgentModal = ({ opened, onClose, dashboardData, channelType, tenantParameters }) => {
  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <ThemeIcon size="lg" radius="xl" variant="light" color="green">
            <IconRobot size={20} />
          </ThemeIcon>
          <Text fw={700} size="lg">
            Plukto AI Özet Asistanı
          </Text>
        </Group>
      }
      centered
      size="auto"
      radius="md"
      overlayProps={{
        blur: 3,
        opacity: 0.55,
      }}
      styles={{
        header: { backgroundColor: 'var(--mantine-color-green-0)', padding: '15px 20px' },
      }}
    >
      <MarkdownTypewriter remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
        {`Merhaba,

Mart ayında toplam **${dashboardData.totalCount.toLocaleString('tr-TR')} adet ${
          channelType === 'Call' ? 'çağrı' : 'yazışma'
        }** analiz edilmiştir. Analiz sonuçları şu şekildedir:

- **100 puan alan ${
          channelType === 'Call' ? 'çağrı' : 'yazışma'
        } sayısı:** ${dashboardData.fullPointCount.toLocaleString('tr-TR')}
- **${tenantParameters.targetedQualityPoint.toLocaleString('tr-TR')} puan altı kalan ${
          channelType === 'Call' ? 'çağrı' : 'yazışma'
        } sayısı:** ${dashboardData.unPassedTargetQualityPointCount.toLocaleString('tr-TR')}
- **0 puan alan ${channelType === 'Call' ? 'çağrı' : 'yazışma'} sayısı:** ${dashboardData.zeroPointCount.toLocaleString(
          'tr-TR'
        )}

**${
          channelType === 'Call' ? 'Çağrıların' : 'Yazışmaların'
        } ortalama süresi:** ${dashboardData.meanOfDuration.toLocaleString('tr-TR')} saniye  
**Ortalama sessizlik süresi:** ${dashboardData.meanOfSlience.toLocaleString('tr-TR')} saniye

### ${channelType === 'Call' ? 'Çağrılarda' : 'Yazışmalarda'} Tespit Edilen Olumsuz Durumlar:

- **${dashboardData.bannedWordCount} adet** ${
          channelType === 'Call' ? 'çağrıda' : 'yazışmada'
        } yasaklı kelime kullanımı tespit edilmiştir
- **${dashboardData.agentInsultCount} adet** temsilcinin hakaret ettiği  ${
          channelType === 'Call' ? 'çağrı' : 'yazışma'
        } tespit edilmiştir
- **${dashboardData.agentSwearCount} adet** temsilcinin küfür ettiği  ${
          channelType === 'Call' ? 'çağrı' : 'yazışma'
        } tespit edilmiştir

---

## En Çok Çağrı Gelen Top 5 Ana Kategori:

1. **İade**
2. **Teslimat**
3. **Genel Sipariş Sorunları**
4. **Kampanyalar**
5. **Sipariş İşlemleri**

---

### 🟦 İade Kategorisi - Top 5 Alt Kategori:

- İnternet satış iadem nerede / sonucu ne oldu  
- İade talebi oluşturmak istiyorum  
- İnternetten aldığım ürünü mağazadan iade etmek istiyorum  
- Mağazadaki iadem geri ödenmedi  
- İnternet satış iadem geri ödenmedi

### 🟩 Teslimat Kategorisi - Top 5 Alt Kategori:

- Kargom hâlâ gelmedi (6 gün üzeri)  
- Kargo adrese teslimatı yapmadı  
- Kargom teslim edilemeden depoya iade dönmüş  
- Teslimat adresini değiştirebilir miyim  
- Kargo firması seçimi

### 🟨 Genel Sipariş Sorunları - Top 5 Alt Kategori:

3. **Empatik Yaklaşım / Pozitif İçten İletişim**  
   - Toplam: **329 adet** puan kaybı

4. **Ankete Yönlendirme Eksikliği**  
   - Toplam: **203 adet** puan kaybı  
   - Detaylar:  
     - ... çağrıda anket yönlendirmesi hiç yapılmadı  
     - ... çağrıda anket cümlesi hatalı kullanıldı

5. **İsimle Hitap Eksikliği**  
   - Toplam: **180 adet** puan kaybı  
   - Detaylar:  
     - ... çağrıda hiç isimle hitap edilmedi  
     - ... çağrıda eksik hitap edildi`}
      </MarkdownTypewriter>
    </Modal>
  );
};

export default AIAgentModal;
