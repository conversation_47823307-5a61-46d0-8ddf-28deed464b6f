import { Box, Card, Flex, Grid, Group, Skeleton, Stack, Table } from '@mantine/core';

import React from 'react';
const DashboardSkeleton = () => {
  return (
    <Grid mt="md">
      <Grid.Col span={{ md: 5 }}>
        <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
          <Skeleton height={30} width={200} mb="lg" />
          <Skeleton height={300} radius="md" />
        </Card>
      </Grid.Col>
      <Grid.Col span={{ md: 7 }}>
        <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
          <Skeleton height={30} width={200} mb="lg" />
          {Array(3)
            .fill(0)
            .map((_, i) => (
              <React.Fragment key={i}>
                <Skeleton height={30} width="100%" mb="sm" />
                <Skeleton height={40} width="100%" mb="md" />
              </React.Fragment>
            ))}
        </Card>
      </Grid.Col>
      <Grid.Col span={{ md: 5 }}>
        <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
          <Skeleton height={30} width={200} mb="lg" />
          <Flex direction={{ base: 'column', md: 'row' }} align="start" gap="xl" wrap="wrap">
            <Box mx="auto">
              <Skeleton height={300} width={300} circle />
            </Box>
            <Stack style={{ width: '100%' }}>
              <Skeleton height={20} width="40%" />
              <Skeleton height={30} width="100%" />
              <Skeleton height={20} width="40%" />
              <Skeleton height={30} width="100%" />
            </Stack>
          </Flex>
        </Card>
      </Grid.Col>
      <Grid.Col span={{ md: 7 }}>
        <Grid>
          <Grid.Col span={{ md: 12 }}>
            <Card withBorder p="md" radius="md">
              <Skeleton height={30} width={200} mb="lg" />
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>
                      <Skeleton height={20} />
                    </Table.Th>
                    <Table.Th>
                      <Skeleton height={20} />
                    </Table.Th>
                    <Table.Th>
                      <Skeleton height={20} />
                    </Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {Array(5)
                    .fill(0)
                    .map((_, i) => (
                      <Table.Tr key={i}>
                        <Table.Td>
                          <Skeleton height={20} />
                        </Table.Td>
                        <Table.Td>
                          <Skeleton height={20} />
                        </Table.Td>
                        <Table.Td>
                          <Skeleton height={20} />
                        </Table.Td>
                      </Table.Tr>
                    ))}
                </Table.Tbody>
              </Table>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ md: 12 }}>
            <Card withBorder p="md" radius="md">
              <Skeleton height={30} width={200} mb="lg" />
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>
                      <Skeleton height={20} />
                    </Table.Th>
                    <Table.Th>
                      <Skeleton height={20} />
                    </Table.Th>
                    <Table.Th>
                      <Skeleton height={20} />
                    </Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {Array(5)
                    .fill(0)
                    .map((_, i) => (
                      <Table.Tr key={i}>
                        <Table.Td>
                          <Skeleton height={20} />
                        </Table.Td>
                        <Table.Td>
                          <Skeleton height={20} />
                        </Table.Td>
                        <Table.Td>
                          <Skeleton height={20} />
                        </Table.Td>
                      </Table.Tr>
                    ))}
                </Table.Tbody>
              </Table>
            </Card>
          </Grid.Col>
        </Grid>
      </Grid.Col>
      <Grid.Col span={{ md: 12 }}>
        <Card withBorder p="md" radius="md" mt="md">
          <Skeleton height={30} width={200} mb="lg" />
          <Skeleton height={400} width="100%" radius="md" />
        </Card>
      </Grid.Col>
    </Grid>
  );
};

export default DashboardSkeleton;
