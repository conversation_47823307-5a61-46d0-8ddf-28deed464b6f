export const formatChartData = (dashboardData) => {
  const allDates = [
    ...new Set([...Object.keys(dashboardData.countByDate || {}), ...Object.keys(dashboardData.pointMeanByDate || {})]),
  ];
  const sortedDates = allDates.map((dateStr) => new Date(dateStr)).sort((a, b) => a - b);
  const dateType = dashboardData.dateType;
  const pad = (n) => String(n).padStart(2, '0');
  const toTRISOString = (date) => {
    const localDate = new Date(date.toLocaleString('en-US', { timeZone: 'Europe/Istanbul' }));
    return (
      localDate.getFullYear() +
      '-' +
      pad(localDate.getMonth() + 1) +
      '-' +
      pad(localDate.getDate()) +
      'T' +
      pad(localDate.getHours()) +
      ':' +
      pad(localDate.getMinutes()) +
      ':' +
      pad(localDate.getSeconds())
    );
  };
  if (dateType === 'HOURLY') {
    return sortedDates.map((date) => {
      const dateStr = toTRISOString(date);
      return {
        date: date.toLocaleTimeString('tr-TR', { day: 'numeric', month: 'short', hour: '2-digit', minute: '2-digit' }),
        score: dashboardData.pointMeanByDate[dateStr],
        count: dashboardData.countByDate[dateStr],
        rawDate: date,
      };
    });
  }
  if (dateType === 'DAILY') {
    return sortedDates.map((date) => {
      const dateStr = date.toISOString().split('.')[0] + 'Z';
      return {
        date: date.toLocaleDateString('tr-TR', { day: 'numeric', month: 'short' }),
        score: dashboardData.pointMeanByDate[dateStr],
        count: dashboardData.countByDate[dateStr],
        rawDate: date,
      };
    });
  }
  if (dateType === 'MONTHLY') {
    return sortedDates.map((date) => {
      const dateStr = toTRISOString(date);
      console.log(dateStr);
      return {
        date: date.toLocaleDateString('tr-TR', { month: 'short', year: 'numeric' }),
        score: dashboardData.pointMeanByDate[dateStr],
        count: dashboardData.countByDate[dateStr],
        rawDate: date,
      };
    });
  }

  return [];
};
