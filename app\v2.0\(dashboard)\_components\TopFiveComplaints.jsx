import { BarChart } from '@mantine/charts';
import { Card, Paper, Text, Group, Box } from '@mantine/core';
import React from 'react';

const TopFiveComplaints = ({ dashboardDataCall, dashboardDataChat, onItemClick }) => {
  const topFiveSubCategories = (() => {
    const combinedSubCategoryName = Object.keys(dashboardDataCall.subCategoryMap).concat(
      Object.keys(dashboardDataChat.subCategoryMap)
    );
    const subCategoryNames = combinedSubCategoryName.filter((item, idx) => {
      return combinedSubCategoryName.indexOf(item) === idx;
    });
    const totalSubCategory = [];
    subCategoryNames.forEach((subName) => {
      totalSubCategory.push({
        subName,
        Çağrı: dashboardDataCall.subCategoryMap[subName] ?? 0,
        Yazışma: dashboardDataChat.subCategoryMap[subName] ?? 0,
        total: (dashboardDataCall.subCategoryMap[subName] ?? 0) + (dashboardDataChat.subCategoryMap[subName] ?? 0),
      });
    });
    return totalSubCategory.sort((a, b) => b.total - a.total).slice(0, 5);
  })();

  const subCategoryTotalCount = topFiveSubCategories.reduce((acc, curr) => acc + curr.total, 0);

  return (
    <Card withBorder p="md" radius="md" mt="md">
      <Text c="black" size="xl" tt="uppercase" fw={700} mb="md">
        TOP 5 ŞİKAYET
      </Text>
      <Group align="flex-start" noWrap>
        <Box style={{ flex: 1, minWidth: 0 }}>
          <BarChart
            h={400}
            data={topFiveSubCategories}
            dataKey="subName"
            orientation="vertical"
            yAxisProps={{ width: 200, tick: { fill: 'black' } }}
            withLegend
            legendProps={{ verticalAlign: 'bottom', height: 50 }}
            barProps={{ radius: 10 }}
            tooltipProps={{
              content: ({ label, payload }) => (
                <Paper px="md" py="sm" withBorder shadow="md" radius="md">
                  <Text fw={'bold'} mb={5}>
                    {label + '   '}
                    {(() => {
                      const categoryData = topFiveSubCategories.find((sc) => sc.subName === label);
                      const categoryTotal = categoryData ? categoryData.total : 0;
                      if (subCategoryTotalCount === 0) {
                        return <>0%</>;
                      }
                      const ratio = (categoryTotal / subCategoryTotalCount) * 100;
                      return `${ratio.toFixed(2)}%`;
                    })()}
                  </Text>
                  {payload.map((item) => (
                    <div style={{ display: 'flex', alignItems: 'center' }} key={item.name + item.value}>
                      <Text fw={'bold'} key={item.name + item.value + '2'} fz="sm">
                        {item.name}:
                      </Text>
                      <Text me={'xs'} ml={'xs'} key={item.name + item.value + '1'} fz="sm">
                        {item.value}
                      </Text>
                    </div>
                  ))}
                </Paper>
              ),
            }}
            series={[
              { name: 'Çağrı', color: 'blue' },
              { name: 'Yazışma', color: 'yellow' },
            ]}
          />
        </Box>
        <div style={{ width: '80px', flexShrink: 0 }}>
          {topFiveSubCategories.map((category) => (
            <div key={category.subName}>
              <div>
                <Box
                  style={{
                    cursor: 'pointer',
                    color: 'white',
                    backgroundColor: '#1971c2',
                    padding: '2.5px 6px',
                    borderRadius: '4px',
                    display: 'inline-block',
                    fontWeight: 600,
                    minWidth: '50px',
                    textAlign: 'center',
                  }}
                  onClick={() => {
                    if (onItemClick) {
                      onItemClick({
                        type: 'Call',
                        subCategory: category.subName,
                      });
                    } else {
                      window.location.href = `/${
                        process.env.VERSION
                      }/channels?channelType=Call&subCategory=${encodeURIComponent(category.subName)}`;
                    }
                  }}
                >
                  {category.Çağrı.toLocaleString('tr-TR')}
                </Box>
                <Box
                  style={{
                    cursor: 'pointer',
                    textAlign: 'center',
                    color: 'white',
                    backgroundColor: '#ff9500',
                    marginBottom: '5px',
                    padding: '2.5px 6px',
                    borderRadius: '4px',
                    display: 'inline-block',
                    fontWeight: 600,
                    minWidth: '50px',
                  }}
                  onClick={() => {
                    if (onItemClick) {
                      onItemClick({
                        type: 'Chat',
                        subCategory: category.subName,
                      });
                    } else {
                      window.location.href = `/${
                        process.env.VERSION
                      }/channels?channelType=Chat&subCategory=${encodeURIComponent(category.subName)}`;
                    }
                  }}
                >
                  {category.Yazışma.toLocaleString('tr-TR')}
                </Box>
              </div>
            </div>
          ))}
        </div>
      </Group>
    </Card>
  );
};

export default TopFiveComplaints;
