import { useState } from 'react';

export const useDashboardData = (fetchAuthClient, channelType) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [oneMonthBeforeDashboardData, setOneMonthBeforeDashboardData] = useState(null);

  const fetchData = async (filters) => {
    try {
      const dashboardResponse = await fetchAuthClient(`${channelType}/dashboard`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      });
      
      const dashboardJson = await dashboardResponse.json();
      setDashboardData(dashboardJson);

      // Fetch one month before data for comparison
      const now = new Date();
      const oneMonthAgo = new Date(now);
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
      const dates = [oneMonthAgo.toISOString(), now.toISOString()];
      
      const response2 = await fetchAuth<PERSON>lient(`${channelType}/dashboard`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify([{ id: 'date', value: dates }]),
      });
      
      const responseJson2 = await response2.json();
      setOneMonthBeforeDashboardData(responseJson2);
      
      return { currentData: dashboardJson, previousData: responseJson2 };
    } catch (error) {
      console.error(`Error fetching ${channelType} data:`, error);
      return { error };
    }
  };

  return {
    dashboardData,
    oneMonthBeforeDashboardData,
    fetchData,
  };
};
