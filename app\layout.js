export const metadata = {
  favicon: 'favicon.ico',
  title: 'Plukto - Connect All Dots - Plukto.comp',
  description:
    'Conversational analytics listens to customer interactions already happening and offers actionable insights.',
};
import 'bootstrap/dist/css/bootstrap.min.css';
import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/dates/styles.css';
import 'mantine-react-table/styles.css';
import '@mantine/charts/styles.css';
import '@mantine/spotlight/styles.css';
import '@mantine/core/styles/global.css';
import { ColorSchemeScript } from '@mantine/core';
import { SpeedInsights } from '@vercel/speed-insights/next';
import './globals.css';
import { AuthLayout } from '@/common/layouts/AuthLayout';

export default async function RootLayout({ children }) {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  return (
    <html lang="tr" suppressHydrationWarning>
      <head>
        <ColorSchemeScript />
      </head>
      <body>
        <AuthLayout>{children}</AuthLayout>
        <SpeedInsights />
      </body>
    </html>
  );
}
