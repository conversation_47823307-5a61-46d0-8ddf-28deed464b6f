'use client';
import { Checkbox, Radio, ScrollAreaAutosize, TextInput } from '@mantine/core';
import { useState } from 'react';

function CustomListInput({ data, value, multiple = false, onChange }) {
  const [search, setSearch] = useState('');
  const [selected, setSelected] = useState(value ?? (multiple ? [] : value));
  const filteredData = data.filter((item) => item.label.toLowerCase().includes(search.toLowerCase()));
  const handleSelect = (value) => {
    if (multiple) {
      let newSelected;
      if (selected.includes(value)) {
        newSelected = selected.filter((v) => v !== value);
      } else {
        newSelected = [...selected, value];
      }
      setSelected(newSelected);
      onChange?.(newSelected);
    } else {
      setSelected(value);
      onChange?.(value);
    }
  };
  return (
    <div>
      <TextInput placeholder="Arama" mb="xs" value={search} onChange={(e) => setSearch(e.currentTarget.value)} />
      <ScrollAreaAutosize mah={250}>
        {multiple ? (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            {filteredData.map((item, index) => (
              <Checkbox
                mt="md"
                key={index}
                label={item.label}
                checked={selected.includes(item.value)}
                onChange={() => handleSelect(item.value)}
              />
            ))}
          </div>
        ) : (
          <Radio.Group
            value={selected || ''}
            onChange={(val) => handleSelect(val)}
            style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}
          >
            {filteredData.map((item) => (
              <Radio key={item.value} mt="md" value={item.value} label={item.label} />
            ))}
          </Radio.Group>
        )}
      </ScrollAreaAutosize>
    </div>
  );
}

export default CustomListInput;
