import React from 'react';
import { Card, Group, Text, ThemeIcon } from '@mantine/core';
import { IconBan } from '@tabler/icons-react';

const ProhibitedWord = ({ setFilter, setSpecialFilterFilterValue, dashboardData, label }) => {
  return (
    <Card withBorder p="md" radius="md" className='h-full'>

      <div className='flex flex-col justify-between h-full'>
        <Group position="apart" mb={5} noWrap justify='space-between'>
          <Text c="dimmed" size="xs" tt="uppercase" fw={700} lineClamp={1}>
            Yasaklı Kelime
          </Text>
          <ThemeIcon color="gray" variant="light" style={{ color: 'var(--mantine-color-red-6)' }} size={28} radius="md">
            <IconBan size={18} stroke={1.5} />
          </ThemeIcon>
        </Group>
        <Group
        justify='space-between'
          spacing={4}
          style={{
            cursor: 'pointer',
          }}
          onClick={() => {
            setFilter(['bannedWord'], false, setSpecialFilterFilterValue, 'specialFilter');
          }}
        >
          <Text fz="xl" fw={700} style={{ lineHeight: 1 }}>
            {dashboardData.bannedWordCount.toLocaleString('tr-TR')}
          </Text>
          <Text size="xs" c="dimmed" mt={3}>
            {label}
          </Text>
        </Group>
      </div>
    </Card>
  );
};

export default ProhibitedWord;
