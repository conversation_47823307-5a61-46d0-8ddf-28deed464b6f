'use client';
import React from 'react';
import { Alert, Button, Card, Grid, GridCol, Group, Progress, Skeleton, Text } from '@mantine/core';
import { IconTextCaption, IconUser } from '@tabler/icons-react';
import { useEffect, useState, useContext } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { useSearchParams } from 'next/navigation';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import SelectFilterPopover from '@/common/components/filters/SelectFilterPopover';
import TagFilterPopover from '@/common/components/filters/TagFilterPopover';
import VirtualizedResultsList from './_components/VirtualizedResultsList';

export default function ChannelSearch() {
  const { fetchAuthClient, permissions } = useContext(AuthContext);
  const searchParams = useSearchParams();
  const channelType = searchParams.get('channelType');
  let label = '';
  if (channelType === 'Call') {
    label = 'Çağrı';
  } else if (channelType === 'Chat') {
    label = 'Yazışma';
  }

  const [dateFilterValue, setDateFilterValue] = useState([null, null]);
  const [personTypeFilterValue, setPersonTypeFilterValue] = useState(null);
  const [keywordsFilterValue, setKeywordsFilterValue] = useState([]);

  const [searchResponse, setSearchResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [agents, setAgents] = useState([]);
  const [formErrors, setFormErrors] = useState({});
  const [tenantParameters, setTenantParameters] = useState(null);

  const fetchAgents = async () => {
    const response = await fetchAuthClient('Agent/' + channelType, {
      method: 'GET',
    });
    var responseJson = await response.json();
    setAgents(responseJson);
  };

  const fetchTenantParameters = async () => {
    const response = await fetchAuthClient('Tenant/parameters/' + channelType, {
      method: 'GET',
    });
    var responseJson = await response.json();
    setTenantParameters(responseJson);
  };

  const setFilter = (value, isEmpty, setFilterMethod) => {
    setFilterMethod(value);
  };

  const validateFilters = () => {
    const errors = {};
    if (!dateFilterValue || dateFilterValue[0] === null || dateFilterValue[1] === null) {
      errors.StartDateTime = ['Başlangıç tarihi varsayılan olamaz.'];
      errors.EndDateTime = ['Bitiş tarihi varsayılan olamaz.'];
    } else if (dateFilterValue[0] >= dateFilterValue[1]) {
      if (!errors.StartDateTime) errors.StartDateTime = [];
      errors.StartDateTime.push('Bitiş tarihi, başlangıç tarihinden büyük olmalıdır.');
    }

    if (!personTypeFilterValue || !['Hepsi', 'Sadece Agent', 'Sadece Müşteri'].includes(personTypeFilterValue)) {
      errors.PersonType = ["Kişi tipi sadece 'Sadece Müşteri', 'Sadece Agent' veya 'Hepsi' olabilir."];
    }

    if (!keywordsFilterValue || keywordsFilterValue.length === 0) {
      errors.Search = ['Kelime listesi boş olmamalıdır.'];
    }

    return errors;
  };

  const handleSearchFormSubmit = async () => {
    const validationErrors = validateFilters();
    if (Object.keys(validationErrors).length > 0) {
      setFormErrors(validationErrors);
      return;
    }

    setFormErrors({});
    setLoading(true);
    setSearchResponse(null);

    let formData = new FormData();
    formData.set('startDateTime', dateFilterValue[0]?.toISOString());
    formData.set('endDateTime', dateFilterValue[1]?.toISOString());
    formData.set('personType', personTypeFilterValue);

    if (keywordsFilterValue && keywordsFilterValue.length > 0) {
      keywordsFilterValue.forEach((word) => {
        if (word && word.trim()) {
          formData.append('search', word.trim());
        }
      });
    } else {
      formData.append('search', '');
    }

    const response = await fetchAuthClient(channelType + '/search', { method: 'POST', body: formData });
    if (response.ok) {
      const data = await response.json();
      setSearchResponse(data);
    } else {
      const data = await response.text();
      try {
        var dataJsonObject = JSON.parse(data);
        if (dataJsonObject.errors) {
          setFormErrors(dataJsonObject.errors);
        }
      } catch {
        alert('Hata: ' + data);
      }
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchAgents();
    fetchTenantParameters();
  }, []);

  if (!permissions.includes(channelType + '.Search')) {
    window.location.href = '/401';
    return <></>;
  }
  if (!tenantParameters || !agents) {
    return <></>;
  }

  return (
    <>
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group>
          <DateFilterPopover
            value={dateFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setDateFilterValue)}
            label="Tarih Aralığı"
            initDateTypeValue="manuel"
          />
          <SelectFilterPopover
            value={personTypeFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setPersonTypeFilterValue)}
            icon={<IconUser />}
            label="Kişi Tipi"
            data={[
              { value: 'Hepsi', label: 'Hepsi' },
              { value: 'Sadece Agent', label: 'Sadece Agent' },
              { value: 'Sadece Müşteri', label: 'Sadece Müşteri' },
            ]}
          />
          <TagFilterPopover
            value={keywordsFilterValue}
            onChange={(value, isEmpty) => setFilter(value, isEmpty, setKeywordsFilterValue)}
            icon={<IconTextCaption />}
            label="Kelime Listesi"
          />
          <Button style={{ marginLeft: 'auto' }} onClick={handleSearchFormSubmit}>
            Filtrele
          </Button>
        </Group>

        {Object.keys(formErrors).length > 0 && (
          <div style={{ marginTop: '10px' }}>
            {Object.entries(formErrors).map(([field, errors]) => (
              <div key={field} style={{ marginBottom: '5px' }}>
                {errors.map((error, index) => (
                  <small key={index} style={{ color: 'red', display: 'block' }}>
                    {error}
                  </small>
                ))}
              </div>
            ))}
          </div>
        )}
      </Card>
      {loading ? (
        <Skeleton mt="md" style={{ width: '100%', height: '200px' }} />
      ) : (
        <>
          {searchResponse !== null ? (
            <>
              {searchResponse.length > 0 ? (
                <Grid mt="md">
                  <GridCol span={{ md: 12 }}>
                    <VirtualizedResultsList
                      results={searchResponse}
                      channelType={channelType}
                      tenantParameters={tenantParameters}
                      fetchAuthClient={fetchAuthClient}
                      permissions={permissions}
                      label={label}
                      height={600}
                      keywords={keywordsFilterValue}
                    />
                  </GridCol>
                </Grid>
              ) : (
                <Alert variant="light" color="orange">
                  Herhangi bir sonuç bulunamadı
                </Alert>
              )}
            </>
          ) : null}
        </>
      )}
    </>
  );
}
