import React from 'react';
import Link from 'next/link';

const UnAuthorized = () => {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gradient-to-br from-red-50 via-white to-red-100 relative overflow-hidden py-10">
      <div className="absolute top-0 left-0 w-[500px] h-[500px] bg-red-300 opacity-20 rounded-full blur-3xl -z-10 animate-pulse"></div>
      <div className="absolute bottom-0 right-0 w-[500px] h-[500px] bg-red-200 opacity-20 rounded-full blur-3xl -z-10 animate-pulse"></div>
      <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-red-400 opacity-10 rounded-full blur-3xl -z-10 animate-pulse delay-700"></div>
      <div className="absolute bottom-1/3 left-1/3 w-96 h-96 bg-red-100 opacity-30 rounded-full blur-3xl -z-10 animate-pulse delay-1000"></div>

      <div className="absolute top-10 left-10 w-6 h-6 border-t-4 border-l-4 border-red-400 opacity-60"></div>
      <div className="absolute bottom-10 right-10 w-6 h-6 border-b-4 border-r-4 border-red-400 opacity-60"></div>
      <div className="absolute top-10 right-10 w-4 h-4 bg-red-400 rounded-full opacity-60"></div>
      <div className="absolute bottom-10 left-10 w-4 h-4 bg-red-400 rounded-full opacity-60"></div>

      <div className="max-w-5xl w-full mx-4 md:mx-auto flex flex-col md:flex-row rounded-3xl shadow-[0_20px_50px_rgba(220,38,38,0.15)] bg-white/95 backdrop-blur-xl border border-red-100/50 overflow-hidden">
        {/* Left panel */}
        <div className="hidden md:flex flex-col justify-center relative items-center w-1/2 bg-gradient-to-br from-red-500 via-red-600 to-red-700 p-8 overflow-y-hidden">
          <div className="absolute inset-0 bg-[url('/pattern.svg')] opacity-10"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-red-400/20 to-transparent"></div>

          <div className="relative z-10 flex flex-col items-center">
            <div className="bg-white/95 rounded-2xl p-4 shadow-xl mb-6 transform hover:scale-105 transition-transform duration-300">
              <img src="/logo.svg" alt="Plukto Logo" className="w-40 h-24 drop-shadow-xl" />
            </div>

            <h1 className="text-4xl font-extrabold text-white mb-4 text-center drop-shadow-lg">
              Yetkisiz <span className="text-red-200">Erişim</span>
            </h1>

            <p className="text-white text-lg text-center font-medium mb-6 leading-relaxed">
              Bu sayfaya erişim yetkiniz bulunmamaktadır.
              <br />
              Lütfen <span className="text-red-200 font-bold">giriş yapın</span> veya{' '}
              <span className="text-red-200 font-bold">ana sayfaya dönün</span>.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl py-5 px-6 border border-white/20 shadow-lg">
              <ul className="text-white text-base space-y-4">
                <li className="flex items-start">
                  <div className="flex items-center justify-center mr-3 h-6 w-6 rounded-full bg-red-200/20 text-red-200 flex-shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <span>Bu sayfaya erişim için gerekli yetkilere sahip değilsiniz</span>
                </li>
                <li className="flex items-start">
                  <div className="flex items-center justify-center mr-3 h-6 w-6 rounded-full bg-red-200/20 text-red-200 flex-shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <span>Giriş yaparak tekrar deneyebilirsiniz</span>
                </li>
                <li className="flex items-start">
                  <div className="flex items-center justify-center mr-3 h-6 w-6 rounded-full bg-red-200/20 text-red-200 flex-shrink-0 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <span>Yardıma ihtiyacınız varsa destek ekibimizle iletişime geçin</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Right panel */}
        <div className="w-full md:w-1/2 flex flex-col items-center justify-center px-6 py-8 bg-white/95 backdrop-blur-xl">
          <div className="md:hidden flex flex-col items-center mb-6">
            <div className="bg-gradient-to-r from-red-500 to-red-600 p-2 rounded-xl shadow-lg mb-4">
              <img src="/logo.svg" alt="Plukto Logo" className="w-20 h-20 drop-shadow-xl bg-white p-2 rounded-lg" />
            </div>
            <h1 className="text-2xl font-extrabold text-red-600 mb-2 text-center drop-shadow-lg">Yetkisiz Erişim</h1>
          </div>

          <div className="text-center mb-8">
            <div className="w-32 h-32 mx-auto mb-6">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-full w-full text-red-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>
            <h2 className="text-3xl font-bold text-gray-800 mb-4">401</h2>
            <p className="text-gray-600 mb-8">Bu sayfaya erişim yetkiniz bulunmamaktadır.</p>
          </div>

          <div className="space-y-4 w-full max-w-md">
            <Link
              href="/login"
              className="w-full py-3 rounded-xl bg-gradient-to-r from-red-500 to-red-600 text-white font-bold text-lg shadow-lg hover:shadow-red-200/50 hover:brightness-105 hover:scale-[1.02] active:scale-[0.98] transition-all duration-200 flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              Giriş Yap
            </Link>

            <Link
              href="/"
              className="w-full py-3 rounded-xl bg-white text-red-600 font-bold text-lg shadow-lg hover:shadow-red-200/50 hover:bg-red-50 hover:scale-[1.02] active:scale-[0.98] transition-all duration-200 flex items-center justify-center border border-red-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
              Ana Sayfaya Dön
            </Link>
          </div>

          <div className="mt-8 text-sm text-gray-500 text-center">
            © {new Date().getFullYear()} Plukto. Tüm hakları saklıdır.
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnAuthorized;
