// This is a suggested modification to your diffDisplay.js file:

export const diffDisplay = ({ currentValue, previousValue, smaller = false }) => {
  const currentNum = parseFloat(currentValue);
  const previousNum = parseFloat(previousValue);
  
  if (isNaN(currentNum) || isNaN(previousNum)) {
    return null;
  }
  
  const diff = currentNum - previousNum;
  const percentage = previousNum === 0 
    ? (diff > 0 ? 100 : 0)
    : ((diff / Math.abs(previousNum)) * 100);
  
  const isPositive = diff > 0;
  const absolutePercentage = Math.abs(percentage).toFixed(1);
  
  const icon = isPositive 
    ? <IconArrowUpRight size={smaller ? 14 : 20} color="green" /> 
    : <IconArrowDownRight size={smaller ? 14 : 20} color="red" />;
    
  return (
    <Text size={smaller ? "xs" : "sm"} 
          c={isPositive ? "green" : "red"} 
          fw={700} 
          style={smaller ? { whiteSpace: 'nowrap', lineHeight: 1 } : {}}>
      {icon} {absolutePercentage}%
    </Text>
  );
};
