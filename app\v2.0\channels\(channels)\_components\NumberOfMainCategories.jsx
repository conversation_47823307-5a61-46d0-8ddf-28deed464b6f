import React from 'react';
import { Accordion, But<PERSON>, Card, Divider, Group, Progress, Text, Tooltip } from '@mantine/core';
import { IconPoint } from '@tabler/icons-react';

const NumberOfMainCategories = ({
  setFilter,
  setCategoryFilterValue,
  setSubcategoryFilterValue,
  dashboardData,
  tenantParameters,
  label,
  classes,
}) => {
  const colors = [
    '#673AB7', // Koyu Mor
    '#9C27B0', // Mor
    '#3F51B5', // İndigo
    '#FDD835', // Sarı
    '#CDDC39', // Limon Yeşili
    '#8BC34A', // Açık Yeşil
    '#4CAF50', // Yeşil
    '#009688', // Turkuaz
    '#00BCD4', // Cam Mavisi
    '#03A9F4', // Açık Mavi
    '#2196F3', // Mavi
  ];

  const categoryNames = Object.keys(dashboardData.categoryMap);
  const totalCount = Object.values(dashboardData.categoryMap).reduce((sum, count) => sum + count, 0);
  const categoryColorMap = categoryNames.reduce((acc, name, index) => {
    acc[name] = colors[index % colors.length];
    return acc;
  }, {});

  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
        Ana Kategori Sayısı
      </Text>
      <Group justify="space-between">
        <Group align="flex-end" gap="xs">
          <Text fz="xl" fw={700}>
            {Object.keys(dashboardData.categoryMap).length}
          </Text>
        </Group>
      </Group>
      <Progress.Root mt="md" size={40}>
        {categoryNames
          .sort((a, b) => dashboardData.categoryMap[b] - dashboardData.categoryMap[a])
          .map((categoryName) => {
            const categoryCount = dashboardData.categoryMap[categoryName];
            const categoryValue = (categoryCount / totalCount) * 100;
            const categoryPercentage = categoryValue.toFixed(1);
            const color = categoryColorMap[categoryName];
            return (
              <Tooltip
                key={categoryName}
                label={`${categoryName} – ${categoryCount} ${label} (${categoryPercentage}%)`}
              >
                <Progress.Section
                  value={categoryValue}
                  color={color}
                  style={{
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    setFilter([categoryName], false, setCategoryFilterValue, 'category');
                  }}
                >
                  <Progress.Label>
                    <small style={{ fontSize: '12px' }}>{categoryName}</small>
                  </Progress.Label>
                </Progress.Section>
              </Tooltip>
            );
          })}
      </Progress.Root>
      <Divider mt="md" mb="md" />
      <Accordion variant="contained" chevronPosition="left">
        {categoryNames
          .sort((a, b) => dashboardData.categoryMap[b] - dashboardData.categoryMap[a])
          .map((categoryName) => {
            const categoryCount = dashboardData.categoryMap[categoryName];
            const categoryPercentage = ((categoryCount / totalCount) * 100).toFixed(1);
            let subcategories = tenantParameters.categories[categoryName] || [];
            subcategories = subcategories
              .filter((subName) => (dashboardData.subCategoryMap[subName] || 0) > 0)
              .sort((a, b) => (dashboardData.subCategoryMap[b] || 0) - (dashboardData.subCategoryMap[a] || 0));
            return (
              <Accordion.Item key={categoryName} value={categoryName}>
                <Accordion.Control>
                  <Group position="apart" style={{ position: 'relative' }}>
                    <Group style={{ width: '85%' }}>
                      <span style={{ color: categoryColorMap[categoryName] }}>{categoryName}</span>
                      <b>
                        {categoryCount} {label}
                      </b>
                      <span>({categoryPercentage}%)</span>
                    </Group>
                    <Group spacing={4}>
                      <Button
                        size="xs"
                        variant={'outline'}
                        color={'teal'}
                        style={{ position: 'absolute', right: '0' }}
                        className={classes.button}
                        onClick={(e) => {
                          e.stopPropagation();
                          setFilter([categoryName], false, setCategoryFilterValue, 'category');
                        }}
                      >
                        Filtrele
                      </Button>
                    </Group>
                  </Group>
                </Accordion.Control>
                <Accordion.Panel>
                  {subcategories.length > 0 ? (
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        marginLeft: '40px',
                        paddingRight: '40px',
                      }}
                    >
                      {subcategories.map((subName) => {
                        const subCount = dashboardData.subCategoryMap[subName] || 0;
                        const subPercentage = categoryCount ? ((subCount / categoryCount) * 100).toFixed(1) : 0;
                        return (
                          <Group
                            key={subName}
                            position="apart"
                            style={{
                              marginTop: '8px',
                              position: 'relative',
                            }}
                          >
                            <div style={{ width: '85%' }}>
                              <Group>
                                <div
                                  style={{
                                    display: 'flex',
                                    justifyContent: 'start',
                                    alignItems: 'center',
                                    gap: '10px',
                                    width: '100%',
                                  }}
                                >
                                  <IconPoint size={16} />
                                  <Tooltip label={subName}>
                                    <Text truncate style={{ cursor: 'pointer' }}>
                                      {subName}
                                    </Text>
                                  </Tooltip>
                                  -
                                  <span
                                    style={{
                                      display: 'inline-block',
                                      fontSize: '13px',
                                      cursor: 'pointer',
                                    }}
                                  >
                                    <Tooltip label={`${subPercentage}%`}>
                                      <p
                                        style={{
                                          whiteSpace: 'nowrap',
                                          fontWeight: 'bold',
                                          marginTop: '17px',
                                          marginLeft: 'auto',
                                        }}
                                      >
                                        {subCount} {label}
                                      </p>
                                    </Tooltip>
                                  </span>
                                </div>
                              </Group>
                            </div>
                            <Button
                              size="xs"
                              variant={'outline'}
                              color={'teal'}
                              style={{ position: 'absolute', right: '-50px' }}
                              className={classes.button}
                              onClick={() => {
                                setFilter([subName], false, setSubcategoryFilterValue, 'subCategory');
                              }}
                            >
                              Filtrele
                            </Button>
                          </Group>
                        );
                      })}
                    </div>
                  ) : (
                    <div>Alt kategori bulunamadı</div>
                  )}
                </Accordion.Panel>
              </Accordion.Item>
            );
          })}
      </Accordion>
    </Card>
  );
};

export default NumberOfMainCategories;
