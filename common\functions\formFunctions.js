import { notifications } from "@mantine/notifications";
import { IconCheck, IconExclamationCircleFilled } from "@tabler/icons-react";
import React from 'react';
export async function handleFormPostSubmit(
  e,
  form,
  fetchRequest,
  onSuccess,
  onError,
) {
  if (e) {
    e.preventDefault();
  }
  form.clearFieldError("formError");
  const notificationId = notifications.show({
    loading: true,
    title: "Yükleniyor",
    message: "<PERSON>ucu tarafından yanıt verilmesi bekleniyor...",
    autoClose: false,
    withCloseButton: false,
  });
  try {
    const response = await fetchRequest;
    if (response.ok) {
      setFormErrors(form, []);
      notifications.update({
        id: notificationId,
        color: "teal",
        title: "Başarılı",
        message: "İşleminiz başarılı bir şekilde tamamlandı.",
        icon: <IconCheck />,
        loading: false,
        autoClose: 2000,
      });
      if (onSuccess) {
        await onSuccess(response);
      }
    } else {
      const data = await response.text();
      try {
        var dataJsonObject = JSON.parse(data);
        if(dataJsonObject.errors){
          setFormErrors(form, dataJsonObject.errors);
        }
      } catch {
        form.setFieldError("formError", data);
      }
      notifications.update({
        id: notificationId,
        color: "red",
        title: "Başarısız",
        message: "İşlem sırasında bir hata oluştu, lütfen tekrar deneyiniz.",
        icon: (
          <IconExclamationCircleFilled />
        ),
        loading: false,
        autoClose: 2000,
      });
      if (onError) {
        onError();
      }
    }
  } catch (error) {
    notifications.update({
      id: notificationId,
      color: "red",
      title: "Hata",
      message: "Bir hata meydana geldi, lütfen daha sonra tekrar deneyiniz.",
      icon: (
        <IconExclamationCircleFilled />
      ),
      loading: false,
      autoClose: 2000,
    });
    // console.log(error);
    if (onError) {
      onError();
    }
  }
}

export async function handleFetchSubmit(fetchRequest, onSuccess) {
  const notificationId = notifications.show({
    loading: true,
    title: "Yükleniyor",
    message: "Sunucu tarafından yanıt verilmesi bekleniyor...",
    autoClose: false,
    withCloseButton: false,
  });
  try {
    const response = await fetchRequest;
    if (response.ok) {
      notifications.update({
        id: notificationId,
        color: "teal",
        title: "Başarılı",
        message: "İşleminiz başarılı bir şekilde tamamlandı.",
        icon: <IconCheck />,
        loading: false,
        autoClose: 2000,
      });
      if (onSuccess) {
        await onSuccess(response);
      }
    } else {
      const data = await response.text();
      notifications.update({
        id: notificationId,
        color: "red",
        title: "Başarısız",
        message:
          "İşlem sırasında bir hata oluştu, lütfen tekrar deneyiniz.\n" + data,
        icon: (
          <IconExclamationCircleFilled />
        ),
        loading: false,
        autoClose: 2000,
      });
    }
  } catch (error) {
    // console.log(error);
    notifications.update({
      id: notificationId,
      color: "red",
      title: "Hata",
      message: "Bir hata meydana geldi, lütfen daha sonra tekrar deneyiniz.",
      icon: (
        <IconExclamationCircleFilled />
      ),
      loading: false,
      autoClose: 2000,
    });
  }
}

export function setFormErrors(form, data) {
  form.clearErrors();
  const errors = {};
  Object.keys(data).forEach((key) => {
    errors[key] = data[key].join(", ");
  });
  function transformKey(key) {
    return key.charAt(0).toLowerCase() + key.slice(1);
  }
  const transformed = Object.entries(errors).reduce((acc, [key, value]) => {
    const newKey = transformKey(key);
    acc[newKey] = value;
    return acc;
  }, {});
  form.setErrors(transformed);
}

export function convertToFormData(formValues) {
  const formData = new FormData();
  for (let key in formValues) {
    if (formValues.hasOwnProperty(key)) {
      formData.append(key, formValues[key]);
    }
  }
  return formData;
}