'use client';

import React, { useContext, useEffect, useState } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import {
  Avatar,
  Badge,
  Button,
  Card,
  Center,
  Container,
  Divider,
  Grid,
  Group,
  Paper,
  Progress,
  RingProgress,
  ScrollArea,
  SimpleGrid,
  Skeleton,
  Stack,
  Tabs,
  Text,
  ThemeIcon,
  Title,
} from '@mantine/core';
import {
  IconActivity,
  IconAlertTriangle,
  IconArrowLeft,
  IconAward,
  IconCategory,
  IconCheck,
  IconClock,
  IconExclamationCircle,
  IconEye,
  IconMessages,
  IconMoodSad,
  IconMoodSmile,
  IconShield,
  IconStar,
  IconTarget,
  IconTrendingUp,
  IconUser,
  IconUsers,
  IconVolumeOff,
  IconX,
} from '@tabler/icons-react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function AgentDetailPage() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const searchParams = useSearchParams();
  const router = useRouter();
  const channelType = searchParams.get('channelType');
  const agentId = searchParams.get('agentId');
  const [agentData, setAgentData] = useState(null);
  const [loading, setLoading] = useState(true);
 const isChatChannel = () => {
    return channelType === 'Chat';
  };
  console.log('Channel Type:', isChatChannel());

  const fetchAgentDetail = async () => {
    setLoading(true);
    try {
      const response = await fetchAuthClient(`${channelType}/agent-detail/${agentId}`, {
        method: 'GET',
      });

      if (response.ok) {
        const data = await response.json();
        setAgentData(data);
      } else {
        console.error('Failed to fetch agent detail data');
      }
    } catch (error) {
      console.error('Error fetching agent detail data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (channelType && agentId) {
      fetchAgentDetail();
    }
  }, [channelType, agentId]);

  const handleBackClick = () => {
    router.push(`/${process.env.VERSION}/channels/agents?channelType=${channelType}`);
  };

  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'green';
    if (score >= 70) return 'blue';
    if (score >= 50) return 'orange';
    return 'red';
  };

  const getSentimentColor = (sentiment) => {
    if (sentiment >= 70) return 'green';
    if (sentiment >= 50) return 'blue';
    if (sentiment >= 30) return 'orange';
    return 'red';
  };

  if (!permissions.includes(channelType + '.AgentView')) {
    window.location.href = '/401';
    return <></>;
  }

  if (loading && !agentData) {
    return <AgentDetailSkeleton />;
  }

  if (!agentData) {
    return (
      <Container size="xl">
        <Card withBorder p="xl" radius="lg">
          <Button leftSection={<IconArrowLeft size={16} />} variant="subtle" onClick={handleBackClick}>
            Temsilciler Listesine Dön
          </Button>
          <Text mt="md" ta="center" size="lg">
            Temsilci verileri bulunamadı.
          </Text>
        </Card>
      </Container>
    );
  }

  return (
    <Container size="xl">
      <Card withBorder p="xl" radius="lg" mb="xl" bg="gradient-to-r from-blue-50 to-indigo-50">
        <Group justify="space-between" mb="lg">
          <Button leftSection={<IconArrowLeft size={16} />} variant="light" onClick={handleBackClick} size="md">
            Temsilciler Listesine Dön
          </Button>
          <Badge size="lg" color="blue" variant="light">
            Temsilci Detayı
          </Badge>
        </Group>

        <Grid>
          <Grid.Col span={{ base: 12, md: 4, lg: 3 }}>
            <Card withBorder p="md" radius="md" bg="white" shadow="sm" h="100%">
              <Center style={{ height: '100%' }}>
                <Stack align="center" gap="md" className=" w-[110%]">
                  <div className="flex  items-center w-full">
                    <div className="relative  w-fit h-fit">
                      <Avatar
                        size={50}
                        radius={100}
                        color="blue.5"
                        variant="light"
                        style={{
                          border: '3px solid #dfe9fa',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                        }}
                      >
                        <IconUser size={25} />
                      </Avatar>
                      <ThemeIcon
                        color="green"
                        radius="xl"
                        size="sm"
                        style={{
                          position: 'absolute',
                          bottom: 0,
                          right: 0,
                          border: '2px solid white',
                        }}
                      >
                        <IconCheck size={14} />
                      </ThemeIcon>
                    </div>
                    <div className="ml-3 ">
                      <Title order={4} fw={600} mb="xs">
                        {agentData.agent.name} {agentData.agent.surname}
                      </Title>
                      <div className="-mt-2 ">
                        <Badge color="blue.5" variant="light" mb="xs" size="md" radius="sm">
                          {agentData.agent.role || 'Temsilci'}
                        </Badge>
                      </div>
                      <Group justify="center" gap="xs">
                        <Text size="xs" c="dimmed">
                          {agentData.agent.email}
                        </Text>
                      </Group>
                    </div>
                  </div>
                </Stack>
              </Center>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 8, lg: 9 }}>
            <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" style={{ height: '100%' }}>
              <Card
                withBorder
                p="lg"
                radius="md"
                bg="white"
                shadow="sm"
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  height: '100%',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': { transform: 'translateY(-5px)', boxShadow: '0 5px 15px rgba(0, 0, 0, 0.1)' },
                }}
              >
                <div className="flex w-full justify-between items-center">
                  <ThemeIcon color="blue" variant="light" size={40} radius="md" mb="sm">
                    <IconEye size={22} />
                  </ThemeIcon>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">
                    Toplam Görüşme
                  </Text>
                </div>
                <Group justify="space-between" align="flex-end">
                  <Text size="2xl" fw={700} c="blue.6" className="min-w-10 text-center">
                    {agentData.totalCount}
                  </Text>
                  <Badge color="blue.5" variant="dot" size="sm">
                    Tümü
                  </Badge>
                </Group>
              </Card>

              <Card
                withBorder
                p="lg"
                radius="md"
                bg="white"
                shadow="sm"
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  height: '100%',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': { transform: 'translateY(-5px)', boxShadow: '0 5px 15px rgba(0, 0, 0, 0.1)' },
                }}
              >
                <div className="flex w-full justify-between items-center">
                  <ThemeIcon
                    color={getScoreColor(agentData.totalPointMean)}
                    variant="light"
                    size={40}
                    radius="md"
                    mb="sm"
                  >
                    <IconStar size={22} />
                  </ThemeIcon>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">
                    Ortalama Puan
                  </Text>
                </div>
                <Group justify="space-between" align="flex-end">
                  <Text
                    size="2xl"
                    fw={700}
                    c={getScoreColor(agentData.totalPointMean)}
                    className="min-w-10 text-center"
                  >
                    {agentData.totalPointMean?.toFixed(1) || 'N/A'}
                  </Text>
                  <Badge color={getScoreColor(agentData.totalPointMean)} variant="dot" size="sm">
                    {agentData.totalPointMean >= 80
                      ? 'Mükemmel'
                      : agentData.totalPointMean >= 60
                      ? 'İyi'
                      : 'Geliştirilebilir'}
                  </Badge>
                </Group>
              </Card>

              <Card
                withBorder
                p="lg"
                radius="md"
                bg="white"
                shadow="sm"
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  height: '100%',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': { transform: 'translateY(-5px)', boxShadow: '0 5px 15px rgba(0, 0, 0, 0.1)' },
                }}
              >
                <div className="flex w-full justify-between items-center">
                  <ThemeIcon color="teal" variant="light" size={40} radius="md" mb="sm">
                    <IconClock size={22} />
                  </ThemeIcon>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">
                    Ortalama Süre
                  </Text>
                </div>
                <Group justify="space-between" align="flex-end">
                  <Text size="2xl" fw={700} c="teal.7" className="min-w-10 text-center">
                    {formatDuration(agentData.meanOfDuration)}
                  </Text>
                  <Badge color="teal" variant="dot" size="sm">
                    Dakika
                  </Badge>
                </Group>
              </Card>

              <Card
                withBorder
                p="lg"
                radius="md"
                bg="white"
                shadow="sm"
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  height: '100%',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': { transform: 'translateY(-5px)', boxShadow: '0 5px 15px rgba(0, 0, 0, 0.1)' },
                }}
              >
                <div className="flex w-full justify-between items-center">
                  <ThemeIcon color="red" variant="light" size={40} radius="md" mb="sm">
                    <IconExclamationCircle size={22} />
                  </ThemeIcon>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">
                    Kural Hatası
                  </Text>
                </div>
                <Group justify="space-between" align="flex-end">
                  <Text size="2xl" fw={700} c="red.7" className="min-w-10 text-center">
                    {Object.values(agentData.promptFailDict || {}).reduce((a, b) => a + b, 0)}
                  </Text>
                  <Badge
                    color={
                      Object.values(agentData.promptFailDict || {}).reduce((a, b) => a + b, 0) > 5 ? 'red' : 'orange'
                    }
                    variant="dot"
                    size="sm"
                  >
                    Tespit
                  </Badge>
                </Group>
              </Card>
            </SimpleGrid>
          </Grid.Col>
        </Grid>

        {/* Management Team */}
        {(agentData.qualityManagers?.length > 0 || agentData.operationManagers?.length > 0) && (
          <Card withBorder p="lg" radius="md" bg="white" shadow="sm" mt="lg">
            <Text size="lg" fw={600} mb="md" c="dark">
              Yönetim Ekibi
            </Text>
            <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="md">
              {agentData.qualityManagers?.length > 0 && (
                <div>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">
                    Kalite Yöneticileri
                  </Text>
                  <Stack gap="xs">
                    {agentData.qualityManagers.map((manager) => (
                      <Group key={manager.id} gap="sm">
                        <Avatar size="sm" color="green" radius="xl">
                          <IconShield size={16} />
                        </Avatar>
                        <div>
                          <Text size="sm" fw={500}>
                            {manager.name} {manager.surname}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {manager.email}
                          </Text>
                        </div>
                      </Group>
                    ))}
                  </Stack>
                </div>
              )}

              {agentData.operationManagers?.length > 0 && (
                <div>
                  <Text size="sm" fw={500} c="dimmed" mb="xs">
                    Operasyon Yöneticileri
                  </Text>
                  <Stack gap="xs">
                    {agentData.operationManagers.map((manager) => (
                      <Group key={manager.id} gap="sm">
                        <Avatar size="sm" color="blue" radius="xl">
                          <IconUsers size={16} />
                        </Avatar>
                        <div>
                          <Text size="sm" fw={500}>
                            {manager.name} {manager.surname}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {manager.email}
                          </Text>
                        </div>
                      </Group>
                    ))}
                  </Stack>
                </div>
              )}
            </SimpleGrid>
          </Card>
        )}
      </Card>

      {/* Performance Overview */}

      {!isChatChannel() && <Grid mb="xl">
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder p="xl" radius="lg" h="100%">
            <Title order={3} mb="lg" c="dark">
              <Group gap="sm">
                <ThemeIcon color="blue" variant="light">
                  <IconActivity size={20} />
                </ThemeIcon>
                Performans Özeti
              </Group>
            </Title>

            <Center mb="xl">
              <RingProgress
                size={200}
                thickness={20}
                sections={[
                  {
                    value: (agentData.happyCustomerCount / agentData.totalCount) * 100,
                    color: 'green',
                    tooltip: 'Memnun Müşteri',
                  },
                  {
                    value: (agentData.unHappyCustomerCount / agentData.totalCount) * 100,
                    color: 'red',
                    tooltip: 'Memnun Olmayan Müşteri',
                  },
                ]}
                label={
                  <Stack align="center" gap={0}>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                      Ortalama
                    </Text>
                    <Text size="xl" fw={700} c={getScoreColor(agentData.totalPointMean)}>
                      {agentData.totalPointMean?.toFixed(1) || 'N/A'}
                    </Text>
                  </Stack>
                }
              />
            </Center>

            <SimpleGrid cols={2} spacing="md">
              <Paper p="md" bg="green.0" radius="md">
                <Group gap="xs" mb="xs">
                  <ThemeIcon color="green" variant="light" size="sm">
                    <IconMoodSmile size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={500}>
                    Memnun
                  </Text>
                </Group>
                <div className="flex w-full justify-between ">
                  <Text size="lg" fw={700} c="green" className="min-w-5 text-center">
                    {agentData.happyCustomerCount}
                  </Text>
                  <Text size="xs" c="dimmed">
                    %{((agentData.happyCustomerCount / agentData.totalCount) * 100).toFixed(1)}
                  </Text>
                </div>
              </Paper>

              <Paper p="md" bg="red.0" radius="md">
                <Group gap="xs" mb="xs">
                  <ThemeIcon color="red" variant="light" size="sm">
                    <IconMoodSad size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={500}>
                    Memnun Değil
                  </Text>
                </Group>
                <div className="flex w-full justify-between ">
                  <Text size="lg" fw={700} c="red" className="min-w-5 text-center">
                    {agentData.unHappyCustomerCount}
                  </Text>
                  <Text size="xs" c="dimmed">
                    %{((agentData.unHappyCustomerCount / agentData.totalCount) * 100).toFixed(1)}
                  </Text>
                </div>
              </Paper>

              <Paper p="md" bg="blue.0" radius="md">
                <Group gap="xs" mb="xs">
                  <ThemeIcon color="blue" variant="light" size="sm">
                    <IconAward size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={500}>
                    Tam Puan
                  </Text>
                </Group>
                <div className="flex w-full justify-between ">
                  <Text size="lg" fw={700} c="blue" className="min-w-5 text-center">
                    {agentData.fullPointCount}
                  </Text>
                  <Text size="xs" c="dimmed">
                    %{((agentData.fullPointCount / agentData.totalCount) * 100).toFixed(1)}
                  </Text>
                </div>
              </Paper>

              <Paper p="md" bg="orange.0" radius="md">
                <Group gap="xs" mb="xs">
                  <ThemeIcon color="orange" variant="light" size="sm">
                    <IconTarget size={16} />
                  </ThemeIcon>
                  <Text size="sm" fw={500}>
                    Sıfır Puan
                  </Text>
                </Group>
                <div className="flex w-full justify-between ">
                  <Text size="lg" fw={700} c="orange" className="min-w-5 text-center">
                    {agentData.zeroPointCount}
                  </Text>
                  <Text size="xs" c="dimmed">
                    %{((agentData.zeroPointCount / agentData.totalCount) * 100).toFixed(1)}
                  </Text>
                </div>
              </Paper>
            </SimpleGrid>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder p="xl" radius="lg" h="100%">
            <Title order={3} mb="lg" c="dark">
              <Group gap="sm">
                <ThemeIcon color="teal" variant="light">
                  <IconMessages size={20} />
                </ThemeIcon>
                Duygu Analizi
              </Group>
            </Title>

            <SimpleGrid cols={2} spacing="xl" mb="xl">
              <Center>
                <Stack align="center" gap="xs">
                  <RingProgress
                    size={140}
                    thickness={12}
                    sections={[
                      {
                        value: agentData.meanOfCustomerSentiment,
                        color: getSentimentColor(agentData.meanOfCustomerSentiment),
                      },
                    ]}
                    label={
                      <Text size="lg" fw={700} ta="center" c={getSentimentColor(agentData.meanOfCustomerSentiment)}>
                        {agentData.meanOfCustomerSentiment?.toFixed(1)}
                      </Text>
                    }
                  />
                  <Text size="sm" fw={500} ta="center">
                    Müşteri Duygusu
                  </Text>
                </Stack>
              </Center>

              <Center>
                <Stack align="center" gap="xs">
                  <RingProgress
                    size={140}
                    thickness={12}
                    sections={[
                      {
                        value: agentData.meanOfAgentSentiment,
                        color: getSentimentColor(agentData.meanOfAgentSentiment),
                      },
                    ]}
                    label={
                      <Text size="lg" fw={700} ta="center" c={getSentimentColor(agentData.meanOfAgentSentiment)}>
                        {agentData.meanOfAgentSentiment?.toFixed(1)}
                      </Text>
                    }
                  />
                  <Text size="sm" fw={500} ta="center">
                    Temsilci Duygusu
                  </Text>
                </Stack>
              </Center>
            </SimpleGrid>

            <Divider mb="md" />

            <Stack gap="md">
              <Group justify="space-between">
                <Text size="sm" c="dimmed">
                  Analiz Tamamlanan
                </Text>
                <Badge color="green" variant="light">
                  {agentData.analysisCompletedCount} / {agentData.totalCount}
                </Badge>
              </Group>
              <Progress
                value={(agentData.analysisCompletedCount / agentData.totalCount) * 100}
                color="green"
                size="lg"
                radius="xl"
              />
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>}

      {/* Detailed Analytics Tabs */}
      <Card withBorder p="xl" radius="lg">
        <Tabs defaultValue="quality" variant="pills">
          <Tabs.List mb="xl">
            <Tabs.Tab value="quality" leftSection={<IconShield size={16} />}>
              Kalite Analizi
            </Tabs.Tab>
            <Tabs.Tab value="categories" leftSection={<IconCategory size={16} />}>
              Kategori Dağılımı
            </Tabs.Tab>
            <Tabs.Tab value="alarms" leftSection={<IconAlertTriangle size={16} />}>
              Alarm & Yasaklı Kelimeler
            </Tabs.Tab>
            <Tabs.Tab value="durations" leftSection={<IconClock size={16} />}>
              Süre Metrikleri
            </Tabs.Tab>
            <Tabs.Tab value="keywords" leftSection={<IconMessages size={16} />}>
              Anahtar Kelimeler
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="quality" className="min-h-[500px]">
            <Grid>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Title order={4} mb="md" c="green">
                  Başarılı Kriterler
                </Title>
                <ScrollArea h={500}>
                  <Stack gap="xs">
                    {Object.entries(agentData.promptSuccesDict || {}).map(([rule, count]) => (
                      <Paper key={rule} p="md" bg="green.0" radius="md">
                        <Group justify="space-between">
                          <Group gap="xs">
                            <ThemeIcon color="green" variant="light" size="sm">
                              <IconCheck size={16} />
                            </ThemeIcon>
                            <Text size="sm">{rule}</Text>
                          </Group>
                          <Badge color="green" variant="light">
                            {count}
                          </Badge>
                        </Group>
                      </Paper>
                    ))}
                  </Stack>
                </ScrollArea>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 6 }}>
                <Title order={4} mb="md" c="red">
                  Başarısız Kriterler
                </Title>
                <ScrollArea h={500}>
                  <Stack gap="xs">
                    {Object.entries(agentData.promptFailDict || {}).length > 0 ? (
                      Object.entries(agentData.promptFailDict || {}).map(([rule, count]) => (
                        <Paper key={rule} p="md" bg="red.0" radius="md">
                          <Group justify="space-between">
                            <Group gap="xs">
                              <ThemeIcon color="red" variant="light" size="sm">
                                <IconX size={16} />
                              </ThemeIcon>
                              <Text size="sm">{rule}</Text>
                            </Group>
                            <Badge color="red" variant="light">
                              {count}
                            </Badge>
                          </Group>
                        </Paper>
                      ))
                    ) : (
                      <Paper p="xl" bg="gray.0" radius="md" style={{ height: '100%' }}>
                        <Stack align="center" justify="center" style={{ height: '100%' }}>
                          <ThemeIcon size={60} color="green" variant="light" radius="xl">
                            <IconCheck size={30} />
                          </ThemeIcon>
                          <Text size="lg" fw={500} ta="center" c="green">
                            Harika! Başarısız kriter bulunmamaktadır.
                          </Text>
                          <Text size="sm" c="dimmed" ta="center">
                            Bu temsilci tüm kalite kriterlerini başarıyla karşılıyor.
                          </Text>
                        </Stack>
                      </Paper>
                    )}
                  </Stack>
                </ScrollArea>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="categories" className="min-h-[500px]">
            <SimpleGrid cols={{ base: 1, md: 2 }} spacing="xl">
              <div>
                <Title order={4} mb="md">
                  Ana Kategoriler
                </Title>
                <ScrollArea h={500}>
                  <Stack gap="xs">
                    {Object.entries(agentData.categoryMap || {}).map(([category, count]) => (
                      <Paper key={category} p="md" withBorder radius="md">
                        <Group justify="space-between">
                          <Text size="sm">{category}</Text>
                          <Badge color="blue">{count}</Badge>
                        </Group>
                      </Paper>
                    ))}
                  </Stack>
                </ScrollArea>
              </div>

              <div>
                <Title order={4} mb="md">
                  Alt Kategoriler
                </Title>
                <ScrollArea h={500}>
                  <Stack gap="xs">
                    {Object.entries(agentData.subCategoryMap || {}).map(([subCategory, count]) => (
                      <Paper key={subCategory} p="md" withBorder radius="md">
                        <Group justify="space-between">
                          <Text size="sm">{subCategory}</Text>
                          <Badge color="teal">{count}</Badge>
                        </Group>
                      </Paper>
                    ))}
                  </Stack>
                </ScrollArea>
              </div>
            </SimpleGrid>
          </Tabs.Panel>

          <Tabs.Panel value="alarms" className="min-h-[500px]">
            <Grid>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder p="lg" radius="md" bg="red.0">
                  <Title order={4} mb="md" c="red">
                    Küfür & Hakaret
                  </Title>
                  <SimpleGrid cols={2} spacing="md">
                    <Paper p="md" bg="white" radius="md">
                      <Text ta="center" size="sm" c="dimmed">
                        Temsilci Küfür
                      </Text>
                      <Text ta="center" fw={700} c="red" size="lg">
                        {agentData.agentSwearCount}
                      </Text>
                    </Paper>
                    <Paper p="md" bg="white" radius="md">
                      <Text ta="center" size="sm" c="dimmed">
                        Temsilci Hakaret
                      </Text>
                      <Text ta="center" fw={700} c="red" size="lg">
                        {agentData.agentInsultCount}
                      </Text>
                    </Paper>
                    <Paper p="md" bg="white" radius="md">
                      <Text ta="center" size="sm" c="dimmed">
                        Müşteri Küfür
                      </Text>
                      <Text ta="center" fw={700} c="orange" size="lg">
                        {agentData.customerSwearCount}
                      </Text>
                    </Paper>
                    <Paper p="md" bg="white" radius="md">
                      <Text ta="center" size="sm" c="dimmed">
                        Müşteri Hakaret
                      </Text>
                      <Text ta="center" fw={700} c="orange" size="lg">
                        {agentData.customerInsultCount}
                      </Text>
                    </Paper>
                  </SimpleGrid>
                </Card>
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 6 }}>
                <Stack gap="md">
                  <Card withBorder p="lg" radius="md" bg="orange.0">
                    <Group justify="space-between" mb="md">
                      <Title order={4} c="orange">
                        Yasaklı Kelimeler
                      </Title>
                      <Badge color="orange" size="lg">
                        {agentData.bannedWordCount}
                      </Badge>
                    </Group>
                    <Text size="sm">
                      Toplam{' '}
                      <Text span fw={700}>
                        {agentData.bannedWordCount}
                      </Text>{' '}
                      yasaklı kelime kullanımı tespit edildi.
                    </Text>
                  </Card>

                  <Card withBorder p="lg" radius="md" bg="yellow.0">
                    <Title order={4} mb="md" c="orange">
                      Alarm Kategorileri
                    </Title>
                    <ScrollArea h={300}>
                      <Stack gap="xs">
                        {Object.entries(agentData.alarmCategories || {})
                          .filter(([_, count]) => count > 0)
                          .map(([category, count]) => (
                            <Group key={category} justify="space-between">
                              <Text size="sm">{category}</Text>
                              <Badge color="orange">{count}</Badge>
                            </Group>
                          ))}
                        {Object.values(agentData.alarmCategories || {}).every((count) => count === 0) && (
                          <Text size="sm" c="dimmed" ta="center">
                            Alarm kategorisi bulunamadı
                          </Text>
                        )}
                      </Stack>
                    </ScrollArea>
                  </Card>
                </Stack>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="durations" className="min-h-[500px]">
            <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="xl">
              <Card withBorder p="lg" radius="md" bg="blue.0" className="h-[200px] ">
                <Group gap="xs" mb="md">
                  <ThemeIcon color="blue" variant="light">
                    <IconClock size={20} />
                  </ThemeIcon>
                  <Title order={4} c="blue">
                    Görüşme Süreleri
                  </Title>
                </Group>
                <Stack gap="md">
                  <div className="flex  justify-around mt-3">
                    <div>
                      <Text size="sm" c="dimmed" mb="xs">
                        Ortalama
                      </Text>
                      <Text size="xl" fw={700} c="blue" className="min-w-10 text-center">
                        {formatDuration(agentData.meanOfDuration)}
                      </Text>
                    </div>
                    <div>
                      <Text size="sm" c="dimmed" mb="xs">
                        Maksimum
                      </Text>
                      <Text size="lg" fw={600} className="min-w-10 text-center">
                        {formatDuration(agentData.maxOfDuration)}
                      </Text>
                    </div>
                    <div>
                      <Text size="sm" c="dimmed" mb="xs">
                        Minimum
                      </Text>
                      <Text size="lg" fw={600} className="min-w-10 text-center">
                        {formatDuration(agentData.minOfDuration)}
                      </Text>
                    </div>
                  </div>
                </Stack>
              </Card>

              <Card withBorder p="lg" radius="md" bg="orange.0" className="h-[200px] ">
                <Group gap="xs" mb="md">
                  <ThemeIcon color="orange" variant="light">
                    <IconVolumeOff size={20} />
                  </ThemeIcon>
                  <Title order={4} c="orange">
                    Sessizlik Süreleri
                  </Title>
                </Group>
                <Stack gap="md">
                  <div className="flex  justify-around mt-3">
                    <div>
                      <Text size="sm" c="dimmed" mb="xs">
                        Ortalama
                      </Text>
                      <Text size="xl" fw={700} c="orange" className="min-w-10 text-center">
                        {formatDuration(agentData.meanOfSlience)}
                      </Text>
                    </div>
                    <div>
                      <Text size="sm" c="dimmed" mb="xs">
                        Maksimum
                      </Text>
                      <Text size="lg" fw={600} className="min-w-10 text-center">
                        {formatDuration(agentData.maxOfSlience)}
                      </Text>
                    </div>
                    <div>
                      <Text size="sm" c="dimmed" mb="xs">
                        Minimum
                      </Text>
                      <Text size="lg" fw={600} className="min-w-10 text-center">
                        {agentData.minOfSlience}s
                      </Text>
                    </div>
                  </div>
                </Stack>
              </Card>

              <Card withBorder p="lg" radius="md" bg="teal.0" className="h-[200px] ">
                <Group gap="xs" mb="md">
                  <ThemeIcon color="teal" variant="light">
                    <IconTrendingUp size={20} />
                  </ThemeIcon>
                  <Title order={4} c="teal">
                    Performans İstatistikleri
                  </Title>
                </Group>
                <Stack gap="md">
                  <div className="flex  justify-around mt-3">
                    <div>
                      <Text size="sm" c="dimmed" mb="xs">
                        Hedef Geçen
                      </Text>
                      <Text size="lg" fw={700} c="green" className="min-w-10 text-center">
                        {agentData.passedTargetQualityPointCount}
                      </Text>
                    </div>
                    <div>
                      <Text size="sm" c="dimmed" mb="xs">
                        Hedef Altında
                      </Text>
                      <Text size="lg" fw={700} c="red" className="min-w-10 text-center">
                        {agentData.unPassedTargetQualityPointCount}
                      </Text>
                    </div>
                    <div>
                      <Text size="sm" c="dimmed" mb="xs">
                        Sessizlik Sayısı
                      </Text>
                      <Text size="lg" fw={700} c="orange" className="min-w-10 text-center">
                        {agentData.slienceCount}
                      </Text>
                    </div>
                  </div>
                </Stack>
              </Card>
            </SimpleGrid>
          </Tabs.Panel>

          <Tabs.Panel value="keywords">
            <Title order={4} mb="lg">
              Sık Kullanılan Anahtar Kelimeler
            </Title>
            <ScrollArea h={500}>
              <SimpleGrid cols={{ base: 2, sm: 3, md: 4, lg: 5 }} spacing="md">
                {Object.entries(agentData.keywords || {}).map(([keyword, count]) => (
                  <Paper key={keyword} p="md" withBorder radius="md" style={{ textAlign: 'center' }}>
                    <Text size="sm" fw={500} mb="xs">
                      {keyword}
                    </Text>
                    <Badge color="blue" variant="light" size="lg">
                      {count}
                    </Badge>
                  </Paper>
                ))}
              </SimpleGrid>
            </ScrollArea>
            {Object.keys(agentData.keywords || {}).length === 0 && (
              <Text ta="center" c="dimmed" size="lg" mt="xl">
                Anahtar kelime bulunamadı
              </Text>
            )}
          </Tabs.Panel>
        </Tabs>
      </Card>
    </Container>
  );
}

const AgentDetailSkeleton = () => {
  return (
    <Container size="xl">
      <Card withBorder p="xl" radius="lg" mb="xl">
        <Group justify="space-between" mb="lg">
          <Skeleton height={36} width={200} radius="md" />
          <Skeleton height={30} width={120} radius="xl" />
        </Group>

        <Grid>
          <Grid.Col span={{ base: 12, md: 4, lg: 3 }}>
            <Card withBorder p="md" radius="md" h="100%">
              <Center style={{ height: '100%' }}>
                <Stack align="center" gap="md" style={{ width: '100%' }}>
                  <Group align="center" position="center">
                    <Skeleton height={50} width={50} radius={100} />
                    <div style={{ width: '70%' }}>
                      <Skeleton height={20} width="80%" mb={10} />
                      <Skeleton height={16} width="60%" mb={5} />
                      <Skeleton height={14} width="90%" />
                    </div>
                  </Group>
                </Stack>
              </Center>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 8, lg: 9 }}>
            <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md">
              {Array(4)
                .fill(0)
                .map((_, i) => (
                  <Card key={i} withBorder p="lg" radius="md" shadow="sm">
                    <Group position="apart" mb="xs">
                      <Skeleton height={40} circle />
                      <Skeleton height={16} width="50%" />
                    </Group>
                    <Group position="apart" mt="md">
                      <Skeleton height={24} width={40} />
                      <Skeleton height={20} width={60} radius="xl" />
                    </Group>
                  </Card>
                ))}
            </SimpleGrid>
          </Grid.Col>
        </Grid>
      </Card>

      <Grid mb="xl">
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder p="xl" radius="lg" h="100%">
            <Skeleton height={30} width="70%" mb="xl" />
            <Center mb="xl">
              <Skeleton height={200} width={200} circle />
            </Center>
            <SimpleGrid cols={2} spacing="md">
              {Array(4)
                .fill(0)
                .map((_, i) => (
                  <Paper key={i} p="md" radius="md">
                    <Group position="apart" mb="xs">
                      <Skeleton height={20} width="60%" />
                      <Skeleton height={20} width="40" />
                    </Group>
                  </Paper>
                ))}
            </SimpleGrid>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder p="xl" radius="lg" h="100%">
            <Skeleton height={30} width="70%" mb="xl" />
            <SimpleGrid cols={2} spacing="xl" mb="xl">
              <Center>
                <Skeleton height={140} width={140} circle />
              </Center>
              <Center>
                <Skeleton height={140} width={140} circle />
              </Center>
            </SimpleGrid>
            <Divider mb="md" />
            <Stack gap="md">
              <Group position="apart">
                <Skeleton height={16} width="40%" />
                <Skeleton height={20} width="80" radius="xl" />
              </Group>
              <Skeleton height={16} radius="xl" />
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>

      <Card withBorder p="xl" radius="lg">
        <Skeleton height={40} width="70%" mb="xl" />
        <Grid>
          <Grid.Col span={12}>
            {Array(5)
              .fill(0)
              .map((_, i) => (
                <Paper key={i} withBorder p="md" radius="md" mb="md">
                  <Group position="apart">
                    <Skeleton height={20} width="60%" />
                    <Skeleton height={20} width="40" radius="xl" />
                  </Group>
                </Paper>
              ))}
          </Grid.Col>
        </Grid>
      </Card>
    </Container>
  );
};
