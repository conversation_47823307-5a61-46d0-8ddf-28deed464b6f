import { AuthContext } from '@/common/contexts/AuthContext';
import { Box, Card, Group, Table, Text, ThemeIcon } from '@mantine/core';
import React, { useContext } from 'react';

const WhereWeMakingMistake = ({ dashboardDataCall, dashboardDataChat, onItemClick }) => {
  const { tenant } = useContext(AuthContext);

  const mergeRulesForLCWaikiki = (promptFailDict, type) => {
    if (tenant !== 'LC WAIKIKI') return promptFailDict;

    const merged = { ...promptFailDict };

    if (type === 'chat') {
      const bekletmeChat = merged['Bekletme Kriterlerine Uyuldu Mu?'] || 0;
      const isimChat = merged['İsim ile Hitap Standartlarına Uyuldu Mu?'] || 0;
      const anketChat = merged['Anket Sunumu Doğru Yapıldı Mı?'] || 0;

      delete merged['Bekletme Kriterlerine Uyuldu Mu?'];
      delete merged['İsim ile Hitap Standartlarına Uyuldu Mu?'];
      delete merged['Anket Sunumu Doğru Yapıldı Mı?'];

      merged['Bekletme Kriterlerinin Dışına Çıkılması'] = (merged['Bekletme Kriterlerinin Dışına Çıkılması'] || 0) + bekletmeChat;
      merged['İsimle Hitap Etme/İsim Soy isim Alma'] = (merged['İsimle Hitap Etme/İsim Soy isim Alma'] || 0) + isimChat;
      merged['Ankete Yönlendirme'] = (merged['Ankete Yönlendirme'] || 0) + anketChat;
    }

    return merged;
  };

  return (
    <Card withBorder p="md" radius="md" style={{ paddingBottom: '8px' }}>
      <Text c="black" size="xl" tt="uppercase" fw={700}>
        NEREDE HATA YAPIYORUZ
      </Text>
      <>
        {(() => {
          const mergedCallData = mergeRulesForLCWaikiki(dashboardDataCall.promptFailDict, 'call');
          const mergedChatData = mergeRulesForLCWaikiki(dashboardDataChat.promptFailDict, 'chat');

          const combinedQualityRulesName = Object.keys(mergedCallData).concat(
            Object.keys(mergedChatData)
          );
          const qualityRulesName = combinedQualityRulesName.filter((item, idx) => {
            return combinedQualityRulesName.indexOf(item) === idx;
          });
          const totalQualityRules = [];
          qualityRulesName.forEach((name) => {
            totalQualityRules.push({
              name,
              Çağrı: mergedCallData[name] ?? 0,
              Yazışma: mergedChatData[name] ?? 0,
              total: (mergedCallData[name] ?? 0) + (mergedChatData[name] ?? 0),
            });
          });
          let qualityRuleTotal = 0;
          let qualityRuleCagriTotal = 0;
          let qualityRuleChatTotal = 0;
          totalQualityRules.forEach((x) => {
            qualityRuleTotal += x.total;
            qualityRuleCagriTotal += x.Çağrı;
            qualityRuleChatTotal += x.Yazışma;
          });
          return (
            <>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ width: '300px' }}>Hata</Table.Th>
                    <Table.Th style={{ textAlign: 'center' }}>Çağrı</Table.Th>
                    <Table.Th style={{ textAlign: 'center' }}>Yazışma</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {totalQualityRules
                    .sort((a, b) => b.total - a.total)
                    .splice(0, 5)
                    .map((x) => (
                      <Table.Tr key={x.name}>
                        <Table.Td style={{ width: '300px' }}>
                          <Group spacing="xs" style={{ display: 'flex', alignItems: 'center' }}>
                            <div className="flex items-center space-x-4">
                              <ThemeIcon color="red" size={8} radius="xl" />
                              <Text fw={500}>{x.name}</Text>
                            </div>
                          </Group>
                        </Table.Td>
                        <Table.Td style={{ textAlign: 'center' }}>
                          <Box
                            style={{
                              cursor: 'pointer',
                              color: 'white',
                              backgroundColor: '#1971c2',
                              padding: '4px 12px',
                              borderRadius: '4px',
                              display: 'inline-block',
                              fontWeight: 600,
                              minWidth: '60px',
                            }}
                            onClick={() => {
                              if (onItemClick) {
                                onItemClick({
                                  type: 'Call',
                                  analysis: x.name,
                                });
                              }
                            }}
                          >
                            {(mergedCallData[x.name] ?? 0).toLocaleString('tr-TR')}
                          </Box>
                        </Table.Td>
                        <Table.Td style={{ textAlign: 'center' }}>
                          <Box
                            style={{
                              cursor: 'pointer',
                              color: 'white',
                              backgroundColor: '#ff9500',
                              padding: '4px 12px',
                              borderRadius: '4px',
                              display: 'inline-block',
                              fontWeight: 600,
                              minWidth: '60px',
                            }}
                            onClick={() => {
                              if (onItemClick) {
                                onItemClick({
                                  type: 'Chat',
                                  analysis: x.name,
                                });
                              }
                            }}
                          >
                            {(mergedChatData[x.name] ?? 0).toLocaleString('tr-TR')}
                          </Box>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                </Table.Tbody>
              </Table>
            </>
          );
        })()}
      </>
    </Card>
  );
};

export default WhereWeMakingMistake;
