'use client';
import React from 'react';

const LoadingSpinner = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50  h-96">
      <div className="flex flex-col items-center ">
        <svg className="animate-spin h-20 w-20 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <div className="mt-3 text-center">
          <h3 className="text-xl font-semibold text-green-700">Yükleniyor</h3>
          <div className="mt-1 flex justify-center">
            <span className="animate-bounce mx-0.5 h-2 w-2 bg-green-600 rounded-full delay-0"></span>
            <span className="animate-bounce mx-0.5 h-2 w-2 bg-green-600 rounded-full delay-150"></span>
            <span className="animate-bounce mx-0.5 h-2 w-2 bg-green-600 rounded-full delay-300"></span>
          </div>
        </div>
      </div>
      <style jsx>{`
        .delay-0 {
          animation-delay: 0ms;
        }
        .delay-150 {
          animation-delay: 150ms;
        }
        .delay-300 {
          animation-delay: 300ms;
        }
      `}</style>
    </div>
  );
};

export default LoadingSpinner;
