'use client';
import React, { useState } from 'react';
import { ActionIcon, Button, Popover, Stack, TagsInput } from '@mantine/core';
import { IconX } from '@tabler/icons-react';

const TagFilterPopover = function ({ label, value, onChange, icon }) {
  const [opened, setOpened] = useState(false);
  const [tempValue, setTempValue] = useState(value);

  const isEmptyValue = (val) => {
    return val.length === 0;
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Popover opened={opened} position="bottom" withArrow>
        <Popover.Target>
          <div>
            <Button
              leftSection={icon}
              size="xs"
              variant="light"
              radius="xl"
              color={isEmptyValue(value) ? 'black' : 'teal'}
              onClick={() => {
                if (opened) {
                  setOpened(false);
                } else {
                  setOpened(true);
                  setTempValue(value);
                }
              }}
            >
              {isEmptyValue(value) ? label : value.join(',')}
            </Button>
          </div>
        </Popover.Target>
        <Popover.Dropdown>
          <div style={{ display: 'flex', width: '100%' }}>
            <ActionIcon
              style={{ marginLeft: 'auto' }}
              variant="subtle"
              color="black"
              onClick={() => {
                setOpened(false);
                setTempValue(value);
              }}
            >
              <IconX />
            </ActionIcon>
          </div>
          <Stack spacing="sm">
            <TagsInput value={tempValue} onChange={(val) => setTempValue(val)} />
            <Button
              onClick={() => {
                onChange(tempValue, isEmptyValue(tempValue));
                setOpened(false);
              }}
              size="xs"
              mt="sm"
            >
              Filtrele
            </Button>
          </Stack>
        </Popover.Dropdown>
      </Popover>
      {isEmptyValue(value) === false && (
        <ActionIcon
          variant="subtle"
          onClick={() => {
            setTempValue([]);
            onChange([], true);
            setOpened(false);
          }}
        >
          <IconX />
        </ActionIcon>
      )}
    </div>
  );
};

export default TagFilterPopover;
