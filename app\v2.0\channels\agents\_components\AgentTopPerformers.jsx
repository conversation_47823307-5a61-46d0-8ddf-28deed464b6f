import React from 'react';
import { Card, Text, Avatar, Group, Stack, Badge, Button, ScrollArea } from '@mantine/core';
import { IconTrophy, IconUser } from '@tabler/icons-react';

const AgentTopPerformers = ({ agentPointListObj, totalCount, onAgentClick }) => {
  if (!agentPointListObj || agentPointListObj.length === 0) {
    return (
      <Card withBorder p="md" radius="md">
        <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
          En İyi Performans Gösteren Temsilciler
        </Text>
        <Text mt="md">Veri bulunamadı</Text>
      </Card>
    );
  }

  const topAgents = [...agentPointListObj]
    .sort((a, b) => b.score - a.score)
    .slice(0, 5);

  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <Text c="dimmed" size="xl" tt="uppercase" fw={700} mb="md">
        En İyi Performans Gösteren Temsilciler
      </Text>
      <Text mb="lg" size="sm" color="dimmed">
        Toplam {totalCount.toLocaleString('tr-TR')} temsilci arasından en yüksek puana sahip 5 temsilci
      </Text>
      
      <ScrollArea h={370}>
        <Stack spacing="md">
          {topAgents.map((agent, index) => (
            <Card key={agent.id} withBorder p="sm" radius="md">
              <Group position="apart">
                <div className='w-full flex justify-between'>
                  <Group>
                    <Avatar color={index === 0 ? 'gold' : index === 1 ? 'silver' : index === 2 ? 'orange' : 'blue'} radius="xl">
                      {index === 0 ? <IconTrophy size={24} /> : <IconUser size={24} />}
                    </Avatar>
                    <div>
                      <Text weight={500}>{agent.name}</Text>
                  
                    </div>
                  </Group>
                  <Group spacing="xs" >
                    <Badge size="lg" variant="filled" color={agent.score >= 90 ? 'green' : agent.score >= 70 ? 'blue' : 'orange'}>
                      {agent.score.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })} Puan
                    </Badge>
                    <Button
                      variant="subtle"
                      size="xs"
                      onClick={() => onAgentClick(agent.id)}
                    >
                      Detaylar
                    </Button>
                  </Group>
                </div>
              </Group>
            </Card>
          ))}
        </Stack>
      </ScrollArea>
    </Card>
  );
};

export default AgentTopPerformers;
