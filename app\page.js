'use client';

import { AuthContext } from '@/common/contexts/AuthContext';
import { useContext } from 'react';

export default function Home() {
  const { user, permissions } = useContext(AuthContext);
  if (user) {
    if (permissions.includes('Call.View') && permissions.includes('Chat.View')) {
      window.location.href = '/' + process.env.VERSION;
      return;
    } else if (permissions.includes('Call.View')) {
      window.location.href = '/' + process.env.VERSION + '/channels?channelType=Call';
      return;
    } else if (permissions.includes('Chat.View')) {
      window.location.href = '/' + process.env.VERSION + '/channels?channelType=Chat';
      return;
    }
  }
  return <></>;
}
