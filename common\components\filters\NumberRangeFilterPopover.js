'use client';
import React, { useState } from 'react';
import { ActionIcon, Button, Group, NumberInput, Popover, Stack } from '@mantine/core';
import { IconX } from '@tabler/icons-react';

const NumberRangeFilterPopover = function ({ label, value, onChange, min, max, icon }) {
  const [opened, setOpened] = useState(false);
  const [tempValue, setTempValue] = useState(value);

  const isEmptyValue = (val) => {
    return Array.isArray(val) && val.length === 2 && val[0] === '' && val[1] === '';
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Popover opened={opened} position="bottom" withArrow>
        <Popover.Target>
          <div>
            <Button
              leftSection={icon}
              size="xs"
              variant="light"
              radius="xl"
              color={isEmptyValue(value) ? 'black' : 'teal'}
              onClick={() => {
                if (opened) {
                  setOpened(false);
                } else {
                  setOpened(true);
                  setTempValue(value);
                }
              }}
            >
              {isEmptyValue(value) ? label : value[0] + '-' + value[1]}
            </Button>
          </div>
        </Popover.Target>
        <Popover.Dropdown>
          <div style={{ display: 'flex', width: '100%' }}>
            <ActionIcon
              style={{ marginLeft: 'auto' }}
              variant="subtle"
              color="black"
              onClick={() => {
                setOpened(false);
                setTempValue(value);
              }}
            >
              <IconX />
            </ActionIcon>
          </div>
          <Stack spacing="sm">
            <Group spacing="xs">
              <NumberInput
                value={tempValue[0]}
                onChange={(val) => setTempValue((prev) => [val, prev[1]])}
                min={min}
                max={max}
              />
              <NumberInput
                value={tempValue[1]}
                onChange={(val) => setTempValue((prev) => [prev[0], val])}
                min={min}
                max={max}
              />
            </Group>
            <Button
              onClick={() => {
                onChange(tempValue, isEmptyValue(tempValue));
                setOpened(false);
              }}
              size="xs"
              mt="sm"
            >
              Filtrele
            </Button>
          </Stack>
        </Popover.Dropdown>
      </Popover>
      {isEmptyValue(value) === false && (
        <ActionIcon
          variant="subtle"
          onClick={() => {
            setTempValue(['', '']);
            onChange(['', ''], true);
            setOpened(false);
          }}
        >
          <IconX />
        </ActionIcon>
      )}
    </div>
  );
};

export default NumberRangeFilterPopover;
