'use client';
import React from 'react';
import { FixedSizeList as List } from 'react-window';
import { Card, Text, Button, Group } from '@mantine/core';
import { IconVolume, IconTextCaption } from '@tabler/icons-react';
import { modals } from '@mantine/modals';
import { ChannelDetailModal } from '../../(channelDetail)/ChannelDetailModal';

const ResultItem = ({ index, style, data }) => {
  const { results, channelType, tenantParameters, fetchAuthClient, permissions, label, keywords } = data;
  const result = results[index];
  return (
    <div style={style}>
      <div style={{ padding: '4px 0' }}>
        <Card padding="sm" shadow="xs" radius="md" withBorder>
          <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '10px' }}>
            <IconVolume size={16} />
            <Text size="sm" weight={500}>
              {result.agentName}
            </Text>
            <Text size="xs" color="dimmed" style={{ marginLeft: 'auto' }}>
              {new Date(result.date).toLocaleDateString('tr-TR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
              })}
            </Text>
          </div>
          <Group position="right" mt="md" style={{ marginLeft: 'auto', justifyContent: 'flex-end' }}>
            <Button
              variant="light"
              color="blue"
              radius="md"
              size="xs"
              rightIcon={<IconTextCaption size={16} />}
              onClick={() => {
                modals.open({
                  title: label + ' Detayı',
                  size: '100%',
                  overlayProps: {
                    backgroundOpacity: 0.55,
                    blur: 3,
                  },
                  radius: 'md',
                  withCloseButton: true,
                  children: (
                    <ChannelDetailModal
                      channelType={channelType}
                      tenantParameters={tenantParameters}
                      id={result.id}
                      fetchAuthClient={fetchAuthClient}
                      permissions={permissions}
                      initSearchTerm={keywords.join(';')}
                    />
                  ),
                });
              }}
            >
              Detaylı İncele
            </Button>
          </Group>
        </Card>
      </div>
    </div>
  );
};

const VirtualizedResultsList = ({
  results,
  channelType,
  tenantParameters,
  fetchAuthClient,
  permissions,
  label,
  keywords,
  height = 600,
}) => {
  return (
    <div style={{ height, width: '100%' }}>
      <List
        height={height}
        itemCount={results.length}
        itemSize={120}
        itemData={{
          results,
          channelType,
          tenantParameters,
          fetchAuthClient,
          permissions,
          label,
          keywords,
        }}
        overscanCount={5}
      >
        {ResultItem}
      </List>
    </div>
  );
};

export default VirtualizedResultsList;
