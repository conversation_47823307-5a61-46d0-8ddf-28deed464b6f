import React from 'react';
import { Card, Divider, Group, Text } from '@mantine/core';
import { Line } from 'react-chartjs-2';
import { formatChartDataForChartJS } from '../_lib/formatChartDataForChartJS';
import { verticalLinePlugin } from '../_lib/verticalLinePlugin';

const LineGraph = ({ dashboardData, chartJsOptions, label }) => {
  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <Group position="apart">
        <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
          {`${label} Puan ve <PERSON>`}
        </Text>
      </Group>
      <Divider mt="md" mb="md" />
      <div style={{ height: '300px', width: '100%' }}>
        <Line
          data={formatChartDataForChartJS(dashboardData, label)}
          options={chartJsOptions}
          plugins={[verticalLinePlugin]}
        />
      </div>
    </Card>
  );
};

export default LineGraph;
