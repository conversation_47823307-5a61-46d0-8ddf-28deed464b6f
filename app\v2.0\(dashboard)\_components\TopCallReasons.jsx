import { Box, Card, Table, Text } from '@mantine/core';
import React from 'react';

const TopCallReasons = ({ dashboardDataCall, dashboardDataChat, onItemClick }) => {
  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <Text c="black" size="xl" tt="uppercase" fw={700}>
        TOP 5 ARAMA NEDENİ
      </Text>
      <>
        {(() => {
          const combinedCategoryName = Object.keys(dashboardDataCall.categoryMap).concat(
            Object.keys(dashboardDataChat.categoryMap)
          );
          const categoryNames = combinedCategoryName.filter((item, idx) => {
            return combinedCategoryName.indexOf(item) === idx;
          });
          const totalCategory = [];
          categoryNames.forEach((name) => {
            totalCategory.push({
              name,
              Çağrı: dashboardDataCall.categoryMap[name] ?? 0,
              Yazışma: dashboardDataChat.categoryMap[name] ?? 0,
              total: (dashboardDataCall.categoryMap[name] ?? 0) + (dashboardDataChat.categoryMap[name] ?? 0),
            });
          });
          let cattotal = 0;
          let catCagriTotal = 0;
          let catChatTotal = 0;
          totalCategory.forEach((x) => {
            cattotal += x.total;
            catCagriTotal += x.Çağrı;
            catChatTotal += x.Yazışma;
          });
          return (
            <>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ width: '300px' }}>Arama Nedeni</Table.Th>
                    <Table.Th style={{ textAlign: 'center' }}>Çağrı</Table.Th>
                    <Table.Th style={{ textAlign: 'center' }}>Yazışma</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {totalCategory
                    .sort((a, b) => b.total - a.total)
                    .splice(0, 5)
                    .map((x) => (
                      <Table.Tr key={x.name}>
                        <Table.Td style={{ width: '300px' }}>
                          <Text fw={500}>{x.name}</Text>
                        </Table.Td>
                        <Table.Td style={{ textAlign: 'center' }}>
                          <Box
                            style={{
                              cursor: 'pointer',
                              color: 'white',
                              backgroundColor: '#1971c2',
                              padding: '4px 12px',
                              borderRadius: '4px',
                              display: 'inline-block',
                              fontWeight: 600,
                              minWidth: '60px',
                            }}
                            onClick={() => {
                              if (onItemClick) {
                                onItemClick({
                                  type: 'Call',
                                  category: x.name,
                                });
                              } else {
                                window.location.href = `/${
                                  process.env.VERSION
                                }/channels?channelType=Call&category=${encodeURIComponent(x.name)}`;
                              }
                            }}
                          >
                            {(dashboardDataCall.categoryMap[x.name] ?? 0).toLocaleString('tr-TR')}
                          </Box>
                        </Table.Td>
                        <Table.Td style={{ textAlign: 'center' }}>
                          <Box
                            style={{
                              cursor: 'pointer',
                              color: 'white',
                              backgroundColor: '#ff9500',
                              padding: '4px 12px',
                              borderRadius: '4px',
                              display: 'inline-block',
                              fontWeight: 600,
                              minWidth: '60px',
                            }}
                            onClick={() => {
                              if (onItemClick) {
                                onItemClick({
                                  type: 'Chat',
                                  category: x.name,
                                });
                              } else {
                                window.location.href = `/${
                                  process.env.VERSION
                                }/channels?channelType=Chat&category=${encodeURIComponent(x.name)}`;
                              }
                            }}
                          >
                            {(dashboardDataChat.categoryMap[x.name] ?? 0).toLocaleString('tr-TR')}
                          </Box>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                </Table.Tbody>
              </Table>
            </>
          );
        })()}
      </>
    </Card>
  );
};

export default TopCallReasons;
