import React from 'react';
import { Accordion, Button, Card, Divider, Group, Progress, Text, Tooltip } from '@mantine/core';
import { IconPoint } from '@tabler/icons-react';

const QualityControlNumber = ({ setFilter, setAnalysisFilterValue, dashboardData, label, classes }) => {
  const colors = [
    '#673AB7', // Koyu Mor
    '#9C27B0', // Mor
    '#3F51B5', // İndigo
    '#FDD835', // Sarı
    '#CDDC39', // Limon Yeşili
    '#8BC34A', // Açık Yeşil
    '#4CAF50', // Yeşil
    '#009688', // Turkuaz
    '#00BCD4', // Cam Mavisi
    '#03A9F4', // Açık Mavi
    '#2196F3', // Mavi
  ];


  const categoryNames = Object.keys(dashboardData.promptFailDict);
  const totalCount = Object.values(dashboardData.promptFailDict).reduce((sum, count) => sum + count, 0);
  const categoryColorMap = categoryNames.reduce((acc, name, index) => {
    acc[name] = colors[index % colors.length];
    return acc;
  }, {});

  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
        Kalite Kuralı Sayısı
      </Text>
      <Group justify="space-between">
        <Group align="flex-end" gap="xs">
          <Text fz="xl" fw={700}>
            {Object.keys(dashboardData.promptFailDict).length}
          </Text>
        </Group>
      </Group>
      <Progress.Root mt="md" size={40}>
        {categoryNames
          .sort((a, b) => dashboardData.promptFailDict[b] - dashboardData.promptFailDict[a])
          .map((categoryName) => {
            const categoryCount = dashboardData.promptFailDict[categoryName];
            const categoryValue = (categoryCount / totalCount) * 100;
            const categoryPercentage = categoryValue.toFixed(1);
            const color = categoryColorMap[categoryName];
            return (
              <Tooltip
                key={categoryName}
                label={`${categoryName} – ${categoryCount} ${label} (${categoryPercentage}%)`}
              >
                <Progress.Section
                  value={categoryValue}
                  color={color}
                  style={{
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    setFilter([categoryName], false, setAnalysisFilterValue, 'analysis');
                  }}
                >
                  <Progress.Label>
                    <small style={{ fontSize: '12px' }}>{categoryName}</small>
                  </Progress.Label>
                </Progress.Section>
              </Tooltip>
            );
          })}
      </Progress.Root>
      <Divider mt="md" mb="md" />
      <Accordion variant="contained" chevronPosition="left" disableChevronRotation chevron={<IconPoint />}>
        {categoryNames
          .sort((a, b) => dashboardData.promptFailDict[b] - dashboardData.promptFailDict[a])
          .map((categoryName) => {
            const categoryCount = dashboardData.promptFailDict[categoryName];
            const categoryPercentage = ((categoryCount / totalCount) * 100).toFixed(1);
            return (
              <Accordion.Item key={categoryName} value={categoryName}>
                <Accordion.Control>
                  <Group position="apart" style={{ position: 'relative' }}>
                    <Group style={{ width: '85%' }}>
                      <span style={{ color: categoryColorMap[categoryName] }}>{categoryName}</span>
                      <b>
                        {categoryCount} {label}
                      </b>
                      <span>({categoryPercentage}%)</span>
                    </Group>
                    <Group spacing={4}>
                      <Button
                        size="xs"
                        variant={'outline'}
                        color="teal"
                        style={{ position: 'absolute', right: '0' }}
                        className={classes.button}
                        onClick={(e) => {
                          e.stopPropagation();
                          setFilter([categoryName], false, setAnalysisFilterValue, 'analysis');
                        }}
                      >
                        Filtrele
                      </Button>
                    </Group>
                  </Group>
                </Accordion.Control>
              </Accordion.Item>
            );
          })}
      </Accordion>
    </Card>
  );
};

export default QualityControlNumber;
