'use client';
import React, { useContext, useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { AuthContext } from '@/common/contexts/AuthContext';
import { Card, Container, Text, Title, Button, Group } from '@mantine/core';
import { IconArrowLeft } from '@tabler/icons-react';
import { ChannelDetailModal } from '../channels/(channelDetail)/ChannelDetailModal';

export default function ChannelDetailPage() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const searchParams = useSearchParams();
  const channelType = searchParams.get('channelType');
  const id = searchParams.get('id');
  const [tenantParameters, setTenantParameters] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!channelType || !id) {
      setError('Geçersiz URL parametreleri. channelType ve id parametreleri gereklidir.');
      setLoading(false);
      return;
    }

    if (!['Call', 'Chat'].includes(channelType)) {
      setError('Geçersiz kanal tipi. Call veya Chat olmalıdır.');
      setLoading(false);
      return;
    }

    if (!permissions.includes(channelType + '.View')) {
      setError('Bu sayfayı görüntülemek için yetkiniz yok.');
      setLoading(false);
      return;
    }

    fetchTenantParameters();
  }, [channelType, id, permissions]);

  const fetchTenantParameters = async () => {
    try {
      const response = await fetchAuthClient('Tenant/parameters/' + channelType, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error('Tenant parametreleri alınamadı');
      }

      const responseJson = await response.json();
      setTenantParameters(responseJson);
    } catch (err) {
      setError('Veri yüklenirken hata oluştu: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    window.history.back();
  };

  const getLabel = () => {
    if (channelType === 'Call') return 'Çağrı';
    if (channelType === 'Chat') return 'Yazışma';
    return channelType;
  };

  if (loading) {
    return (
      <Container size="xl" py="md">
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Text>Yükleniyor...</Text>
        </Card>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="xl" py="md">
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group mb="md">
            <Button variant="light" leftSection={<IconArrowLeft size={16} />} onClick={goBack}>
              Geri Dön
            </Button>
          </Group>
          <Text color="red" size="lg">
            {error}
          </Text>
        </Card>
      </Container>
    );
  }

  return (
    <Container size="xl" py="md">
      <Group mb="md">
        <Button variant="light" leftSection={<IconArrowLeft size={16} />} onClick={goBack}>
          Geri Dön
        </Button>
        <Title order={2}>
          {getLabel()} Detayı - ID: {id}
        </Title>
      </Group>

      <ChannelDetailModal
        channelType={channelType}
        id={id}
        tenantParameters={tenantParameters}
        fetchAuthClient={fetchAuthClient}
        permissions={permissions}
      />
    </Container>
  );
}
