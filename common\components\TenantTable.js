'use client';
import React from 'react';
import { useEffect, useMemo, useState, forwardRef, useImperativeHandle, useContext } from 'react';
import {
  MantineReactTable,
  MRT_ShowHideColumnsButton,
  MRT_ToggleDensePaddingButton,
  useMantineReactTable,
} from 'mantine-react-table';
import {
  ActionIcon,
  Button,
  Drawer,
  Flex,
  Menu,
  Tooltip,
  Modal,
  Text,
  Checkbox,
  ScrollArea,
  Divider,
  Title,
  Paper,
  Group,
  Box,
  Badge,
  Loader,
  NumberInput,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { MRT_Localization_TR } from 'mantine-react-table/locales/tr';
import { useForm } from '@mantine/form';
import { handleFormPostSubmit, handleFetchSubmit, convertToFormData } from '@/common/functions/formFunctions';
import { modals } from '@mantine/modals';
import { FormError } from '@/common/components/FormError';
import { IconFilterCancel, IconPlus, IconRefresh, IconFileExport } from '@tabler/icons-react';
import { AuthContext } from '@/common/contexts/AuthContext';
import ExcelJS from 'exceljs';

export const TenantTable = forwardRef(
  (
    {
      entityType,
      entityText,
      grouping = [],
      initSorting = [],
      columns,
      form,
      entityToUpdate,
      onBeforeFormSubmit = (formData) => {
        return formData;
      },
      hidedDefaultColumns = {},
      onRowClick = (data) => {},
      allowAdd = true,
      allowDelete = true,
      allowUpdate = (data) => {},
      entityToAdd = async () => ({}),
      isTenant = true,
      actionButtons = [],
      urlParameters = '',
      selectedItemActionButtons = [],
      tableButtons = [],
      topIconButtons = [],
      initColumnFilters = [],
      onColumnFiltersChange = (columnFilters) => {},
      stopPropagationSelector = null,
      agents = [],
    },
    ref
  ) => {
    const { fetchAuthClient, tenant, user } = useContext(AuthContext);
    const [rowSelection, setRowSelection] = useState({});
    const [data, setData] = useState([]);
    const [isError, setIsError] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isRefetching, setIsRefetching] = useState(false);
    const [rowCount, setRowCount] = useState(0);
    const [columnFilters, setColumnFilters] = useState(initColumnFilters);
    const [columnVisibility, setColumnVisibility] = useState(hidedDefaultColumns);
    const [showColumnFilters, setShowColumnFilters] = useState(false);
    const [sorting, setSorting] = useState(initSorting);
    const [pagination, setPagination] = useState({
      pageIndex: 0,
      pageSize: 20,
    });
    const [mode, setMode] = useState('');
    const [opened, { open, close }] = useDisclosure(false);
    const [exportModalOpened, setExportModalOpened] = useState(false);
    const [exportColumns, setExportColumns] = useState([]);
    const [lastFetchParams, setLastFetchParams] = useState(null);
    const tableForm = useForm({
      mode: 'controlled',
    });

    const addColumnFilter = (newFilter) => {
      setColumnFilters((prevFilters) => {
        let existingFilter = prevFilters.find((filter) => filter.id === newFilter.id);
        if (existingFilter) {
          let updatedFilters = prevFilters.filter((filter) => filter.id !== newFilter.id);
          existingFilter.value = newFilter.value;
          return [...updatedFilters, existingFilter];
        } else {
          return [...prevFilters, newFilter];
        }
      });
    };

    const removeColumnFilter = (filterId) => {
      setColumnFilters((prevFilters) => {
        const updatedFilters = prevFilters.filter((filter) => filter.id !== filterId);
        return updatedFilters;
      });
    };

    const clearFilter = () => {
      setColumnFilters([]);
    };

    const getColumnFilters = () => {
      return columnFilters;
    };

    useImperativeHandle(ref, () => ({
      setColumnFilters,
      getColumnFilters,
      addColumnFilter,
      removeColumnFilter,
      fetchData,
      clearFilter,
    }));

    const handleTableFormSubmit = async (e) => {
      if (e) {
        e.preventDefault();
      }
      const formValues = tableForm.getValues();
      let formData = convertToFormData(formValues);
      if (onBeforeFormSubmit) {
        formData = onBeforeFormSubmit(formData);
      }
      handleFormPostSubmit(
        e,
        tableForm,
        fetchAuthClient(entityType + '/' + mode + urlParameters, {
          method: 'POST',
          body: formData,
        }),
        async (response) => {
          await fetchData();
          close();
        }
      );
    };
    const handleDelete = async (id) => {
      modals.openConfirmModal({
        title: 'İşlem Onayı',
        children: <span>Bu işlem geri alınamaz, devam etmek istediğinize emin misiniz?</span>,
        labels: { confirm: 'Devam Et', cancel: 'İptal' },
        onConfirm: () => {
          handleFetchSubmit(
            fetchAuthClient(entityType + '/delete/' + id, {
              method: 'POST',
            }),
            async (response) => {
              await fetchData();
            }
          );
        },
      });
    };

    const fetchData = async () => {
      const currentFetchParams = {
        page: pagination.pageIndex + 1,
        pageSize: pagination.pageSize,
        sortFilters: JSON.stringify(sorting),
        columnFilters: JSON.stringify(columnFilters),
      };

      const paramsChanged = !lastFetchParams || JSON.stringify(currentFetchParams) !== JSON.stringify(lastFetchParams);

      if ((isLoading || isRefetching) && !paramsChanged) {
        return;
      }

      setLastFetchParams(currentFetchParams);

      if (isTenant) {
        if (!tenant) {
          alert('Tenant Error!');
          return;
        }
      }

      if (!data.length) {
        setIsLoading(true);
      } else {
        setIsRefetching(true);
      }

      try {
        const response = await fetchAuthClient(entityType + '/paged' + urlParameters, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            page: pagination.pageIndex + 1,
            pageSize: pagination.pageSize,
            sortFilters: sorting,
            columnFilters: columnFilters,
          }),
        });
        const json = await response.json();
        setData(json.items);
        setRowCount(json.totalItems);
      } catch (error) {
        setIsError(true);
        console.error(error);
        return;
      }
      setIsError(false);
      setIsLoading(false);
      setIsRefetching(false);
    };
    useEffect(() => {
      fetchData();
      setRowSelection({});
    }, [columnFilters, pagination.pageIndex, pagination.pageSize, sorting, isTenant, tenant]);
    useEffect(() => {
      onColumnFiltersChange(columnFilters);
    }, [columnFilters]);

    const tableColumns = useMemo(() => columns, [columns]);

    useEffect(() => {
      if (tableColumns && tableColumns.length > 0) {
        const visibleColumns = tableColumns
          .filter((column) => !(column?.adminOnly && user?.role !== 'ADMİN') && !columnVisibility[column.accessorKey])
          .map((column) => column.accessorKey);
        setExportColumns(visibleColumns);
      }
    }, [tableColumns, columnVisibility, user?.role]);

    const [columnWidths, setColumnWidths] = useState(
      tableColumns.reduce((acc, column) => {
        acc[column?.accessorKey] = column?.size || 160;
        return acc;
      }, {})
    );

    const resetColumnWidths = () => {
      table.setColumnSizing(columnWidths);
    };

    const exportToExcel = async (exportAll = false, customCount = null) => {
      try {
        setIsLoading(true);
        let dataToExport = [];
        if (exportAll) {
          const response = await fetchAuthClient(entityType + '/paged' + urlParameters, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              page: 1,
              pageSize: customCount || 100000,
              sortFilters: sorting,
              columnFilters: columnFilters,
            }),
          });
          const json = await response.json();
          dataToExport = json.items;
        } else {
          dataToExport = [...data];
        }

        const visibleColumns = tableColumns.filter((column) => {
          const isGenerallyVisible =
            !(column?.adminOnly && user?.role !== 'ADMİN') &&
            !columnVisibility[column.accessorKey] &&
            exportColumns.includes(column.accessorKey);

          if (!isGenerallyVisible) {
            return false;
          }

          if (column.accessorKey === 'provider') {
            return user?.role === 'ADMİN';
          }
          return true;
        });

        const allQualityCriteria = new Set();
        dataToExport.forEach((item) => {
          if (item.info && item.info.analysis) {
            Object.keys(item.info.analysis).forEach((criterion) => allQualityCriteria.add(criterion));
          }
          if (item.analysis && Array.isArray(item.analysis)) {
            item.analysis.forEach((criterion) => allQualityCriteria.add(criterion.controlCriterion));
          }
        });

        const maxQualityCriteria = Math.min(allQualityCriteria.size, 16384 - visibleColumns.length - 10);
        const limitedQualityCriteria = Array.from(allQualityCriteria).slice(0, maxQualityCriteria);

        if (allQualityCriteria.size > maxQualityCriteria) {
          console.warn(
            `Too many quality criteria (${allQualityCriteria.size}). Limited to ${maxQualityCriteria} to prevent Excel overflow.`
          );
          alert(
            `Uyarı: Çok fazla kalite kriteri bulundu (${allQualityCriteria.size}). Excel sınırları nedeniyle ilk ${maxQualityCriteria} kriter dahil edildi.`
          );
        }

        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Kalite Analizi');

        const headers = [];
        visibleColumns.forEach((column) => {
          if (column.accessorKey === 'date') {
            headers.push('Tarih');
            headers.push('Saat');
          } else {
            headers.push(column.header);
          }
        });

        limitedQualityCriteria.forEach((criterionName) => {
          headers.push(`Kalite - ${criterionName}`);
        });

        worksheet.addRow(headers);

        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
        headerRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '4472C4' },
        };
        headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

        dataToExport.forEach((item) => {
          const row = [];

          visibleColumns.forEach((column) => {
            const key = column.accessorKey;
            if (key === 'agentId') {
              const agent = agents.find((a) => a.id === item.agentId);
              row.push(agent ? agent.name + ' ' + agent.surname : item.agentId || '');
            } else if (key === 'identifier') {
              row.push(item.identifier || item.id);
            } else if (key === 'analysis' && item.info && item.info.analysis) {
              const failed = Object.entries(item.info.analysis)
                .filter(([_, v]) => v.isAgentHaveWrongAction === true)
                .map(([k]) => k);
              row.push(failed.length > 0 ? failed.join(', ') : '');
            } else if (key === 'issueStatus' && item.info) {
              row.push(item.info.issueStatus || '');
            } else if (key === 'category' && item.info) {
              row.push(item.info.category || '');
            } else if (key === 'subCategory' && item.info) {
              row.push(item.info.subCategory || '');
            } else if (key === 'point' && item.info) {
              if (item.info.point === 0) {
                row.push(0);
              } else {
                row.push(item.info.point !== undefined && item.info.point !== null ? Number(item.info.point) : 0);
              }
            } else if (key === 'provider') {
              row.push(item.provider || '');
            } else if (key === 'date') {
              if (item.date) {
                const dateObj = new Date(item.date);
                const formattedDate = dateObj.toLocaleDateString('tr-TR', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                });
                const formattedTime = dateObj.toLocaleTimeString('tr-TR', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false,
                });
                row.push(formattedDate);
                row.push(formattedTime);
              } else {
                row.push('');
                row.push('');
              }
            } else if (key === 'language') {
              row.push(item.language || '');
            } else if (key === 'duration') {
              if (item.duration === 0) {
                row.push(0);
              } else {
                row.push(item.duration !== undefined && item.duration !== null ? Number(item.duration) : 0);
              }
            } else if (key === 'maxSlience') {
              if (item.info && item.info.maxSlience !== undefined && item.info.maxSlience !== null) {
                row.push(Number(item.info.maxSlience.toFixed(2)));
              } else {
                row.push('');
              }
            } else if (key === 'type') {
              row.push(item.type || '');
            } else if (key === 'caller') {
              row.push(item.caller || '');
            } else if (key === 'called') {
              row.push(item.called || '');
            } else if (key === 'agentBadLanguage') {
              const swears = item.info && item.info.agentSwears ? item.info.agentSwears : [];
              const insults = item.info && item.info.agentInsults ? item.info.agentInsults : [];
              const allBadLanguage = [...swears, ...insults];

              if (allBadLanguage.length === 0) {
                row.push('Temiz');
              } else if (allBadLanguage.length === 1) {
                row.push(allBadLanguage[0]);
              } else {
                row.push(`${swears.length} Küfür, ${insults.length} Hakaret: ${allBadLanguage.join(', ')}`);
              }
            } else if (key === 'communicationType' || key === 'callDirection') {
              row.push(item.communicationType || item.callDirection || '');
            } else if (key.includes('.')) {
              const [parent, child] = key.split('.');
              if (item[parent] && item[parent][child] !== undefined) {
                row.push(item[parent][child]);
              } else {
                row.push('');
              }
            } else {
              row.push(item[key] !== undefined ? item[key] : '');
            }
          });

          limitedQualityCriteria.forEach((criterionName) => {
            let description = '';
            if (item.info && item.info.analysis && item.info.analysis[criterionName]) {
              description = item.info.analysis[criterionName].agentWrongActionDescription || '';
            } else if (item.analysis && Array.isArray(item.analysis)) {
              const analysisItem = item.analysis.find((a) => a.controlCriterion === criterionName);
              if (analysisItem) {
                description = analysisItem.description || '';
              }
            }
            row.push(description);
          });

          worksheet.addRow(row);
        });

        worksheet.columns.forEach((column, index) => {
          const dateColumnIndex = visibleColumns.findIndex((col) => col.accessorKey === 'date');
          let adjustedIndex = index;

          if (dateColumnIndex !== -1 && index > dateColumnIndex) {
            adjustedIndex = index - 1;
          }

          if (adjustedIndex < visibleColumns.length) {
            column.width = 20;
          } else {
            column.width = 50;
          }
        });

        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber > 1) {
            row.height = 20;
          }
        });

        const qualityStartColumn = headers.length - limitedQualityCriteria.length + 1;

        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber > 1) {
            const dataIndex = rowNumber - 2;
            const item = dataToExport[dataIndex];

            if (item) {
              limitedQualityCriteria.forEach((criterionName, criterionIndex) => {
                const columnIndex = qualityStartColumn + criterionIndex;

                if (columnIndex > 16384) {
                  console.warn(`Column index ${columnIndex} exceeds Excel limit for criterion: ${criterionName}`);
                  return;
                }

                let isSuccess = false;

                if (item.info && item.info.analysis && item.info.analysis[criterionName]) {
                  isSuccess = !item.info.analysis[criterionName].isAgentHaveWrongAction;
                } else if (item.analysis && Array.isArray(item.analysis)) {
                  const analysisItem = item.analysis.find((a) => a.controlCriterion === criterionName);
                  if (analysisItem) {
                    isSuccess = analysisItem.isSuccess;
                  }
                }

                try {
                  const cell = row.getCell(columnIndex);

                  cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: isSuccess ? 'FF90EE90' : 'FFFFB6C1' },
                  };

                  cell.font = {
                    color: { argb: isSuccess ? 'FF006400' : 'FF8B0000' },
                  };

                  cell.alignment = {
                    wrapText: false,
                    vertical: 'top',
                    horizontal: 'left',
                  };

                  cell.border = {
                    top: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                    bottom: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                    left: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                    right: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                  };
                } catch (error) {
                  console.error(`Error accessing cell at column ${columnIndex} for criterion ${criterionName}:`, error);
                }
              });
            }
          }
        });

        const lastDataRow = dataToExport.length;
        const summaryStartRow = lastDataRow + 3;

        const summaryHeaderRow = worksheet.getRow(summaryStartRow);
        summaryHeaderRow.getCell(1).value = 'İSTATİSTİKLER';
        summaryHeaderRow.getCell(1).font = { bold: true, size: 14, color: { argb: 'FF0066CC' } };
        summaryHeaderRow.getCell(1).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE6F3FF' },
        };

        const numericColumns = ['point', 'duration', 'maxSlience'];
        const averagesRow = worksheet.getRow(summaryStartRow + 1);
        averagesRow.getCell(1).value = 'ORTALAMALAR';
        averagesRow.getCell(1).font = { bold: true };

        numericColumns.forEach((columnKey) => {
          const column = visibleColumns.find((col) => col.accessorKey === columnKey);
          if (column) {
            const columnIndex = visibleColumns.indexOf(column) + 1;
            let sum = 0;
            let count = 0;

            dataToExport.forEach((item) => {
              let value = 0;
              if (columnKey === 'point' && item.info) {
                value = item.info.point;
              } else if (columnKey === 'duration') {
                value = item.duration;
              } else if (columnKey === 'maxSlience') {
                value = item.maxSlience;
              }

              if (value !== undefined && value !== null && !isNaN(value)) {
                sum += Number(value);
                count++;
              }
            });

            if (count > 0) {
              const average = sum / count;
              const cell = averagesRow.getCell(columnIndex);
              cell.value = columnKey === 'duration' ? `${average.toFixed(2)} sn` : average.toFixed(2);
              cell.font = { bold: true };
              // cell.fill = {
              //   type: 'pattern',
              //   pattern: 'solid',
              //   fgColor: { argb: 'FFFFF0E6' },
              // };
              cell.alignment = { horizontal: 'center' };
            }
          }
        });

        // quality criteria success rates
        const successRatesRow = worksheet.getRow(summaryStartRow + 2);
        successRatesRow.getCell(1).value = 'BAŞARI ORANLARI';
        successRatesRow.getCell(1).font = { bold: true };

        limitedQualityCriteria.forEach((criterionName, criterionIndex) => {
          const columnIndex = qualityStartColumn + criterionIndex;

          if (columnIndex > 16384) return;

          let successCount = 0;
          let totalCount = 0;

          dataToExport.forEach((item) => {
            let hasData = false;
            let isSuccess = false;

            if (item.info && item.info.analysis && item.info.analysis[criterionName]) {
              hasData = true;
              isSuccess = !item.info.analysis[criterionName].isAgentHaveWrongAction;
            } else if (item.analysis && Array.isArray(item.analysis)) {
              const analysisItem = item.analysis.find((a) => a.controlCriterion === criterionName);
              if (analysisItem) {
                hasData = true;
                isSuccess = analysisItem.isSuccess;
              }
            }

            if (hasData) {
              totalCount++;
              if (isSuccess) {
                successCount++;
              }
            }
          });

          if (totalCount > 0) {
            const successRate = (successCount / totalCount) * 100;
            const cell = successRatesRow.getCell(columnIndex);
            cell.value = `%${successRate.toFixed(1)} (${successCount}/${totalCount})`;
            cell.font = { bold: true };
            // cell.fill = {
            //   type: 'pattern',
            //   pattern: 'solid',
            //   fgColor: { argb: successRate >= 80 ? 'FFE6FFE6' : successRate >= 60 ? 'FFFFE6E6' : 'FFFFE6E6' },
            // };
            cell.alignment = { horizontal: 'center' };
          }
        });

        const summaryRows = [summaryStartRow, summaryStartRow + 1, summaryStartRow + 2];
        summaryRows.forEach((rowIndex) => {
          const row = worksheet.getRow(rowIndex);
          for (let col = 1; col <= headers.length; col++) {
            const cell = row.getCell(col);
            cell.border = {
              top: { style: 'thin', color: { argb: 'FF808080' } },
              bottom: { style: 'thin', color: { argb: 'FF808080' } },
              left: { style: 'thin', color: { argb: 'FF808080' } },
              right: { style: 'thin', color: { argb: 'FF808080' } },
            };
          }
        });

        // #region Agent Statistics
        const agentStatsSheet = workbook.addWorksheet('Temsilci İstatistikleri');

        const agentStats = {};
        dataToExport.forEach((item) => {
          const agentId = item.agentId;
          const agent = agents.find((a) => a.id === agentId);
          const agentName = agent ? `${agent.name} ${agent.surname}` : `Agent ${agentId}`;

          if (!agentStats[agentId]) {
            agentStats[agentId] = {
              agentName,
              agentId,
              calls: [],
              totalDuration: 0,
              totalPoint: 0,
              totalMaxSlience: 0,
              validDurationCount: 0,
              validPointCount: 0,
              validMaxSlienceCount: 0,
              criteriaStats: {},
            };
          }

          agentStats[agentId].calls.push(item);

          if (item.duration !== undefined && item.duration !== null && !isNaN(item.duration)) {
            agentStats[agentId].totalDuration += Number(item.duration);
            agentStats[agentId].validDurationCount++;
          }

          if (item.info && item.info.point !== undefined && item.info.point !== null && !isNaN(item.info.point)) {
            agentStats[agentId].totalPoint += Number(item.info.point);
            agentStats[agentId].validPointCount++;
          }

          if (
            item.info &&
            item.info.maxSlience !== undefined &&
            item.info.maxSlience !== null &&
            !isNaN(item.info.maxSlience)
          ) {
            agentStats[agentId].totalMaxSlience += Number(item.info.maxSlience);
            agentStats[agentId].validMaxSlienceCount++;
          }

          // quality criteria
          limitedQualityCriteria.forEach((criterionName) => {
            if (!agentStats[agentId].criteriaStats[criterionName]) {
              agentStats[agentId].criteriaStats[criterionName] = {
                total: 0,
                success: 0,
              };
            }

            let hasData = false;
            let isSuccess = false;

            if (item.info && item.info.analysis && item.info.analysis[criterionName]) {
              hasData = true;
              isSuccess = !item.info.analysis[criterionName].isAgentHaveWrongAction;
            } else if (item.analysis && Array.isArray(item.analysis)) {
              const analysisItem = item.analysis.find((a) => a.controlCriterion === criterionName);
              if (analysisItem) {
                hasData = true;
                isSuccess = analysisItem.isSuccess;
              }
            }

            if (hasData) {
              agentStats[agentId].criteriaStats[criterionName].total++;
              if (isSuccess) {
                agentStats[agentId].criteriaStats[criterionName].success++;
              }
            }
          });
        });

        const agentHeaders = [
          'Temsilci Adı',
          'Temsilci ID',
          'Çağrı Sayısı',
          'Ortalama Süre (sn)',
          'Ortalama Puan',
          'Ortalama Max Sessizlik (sn)',
          ...limitedQualityCriteria.map((criterion) => `${criterion} Başarı Oranı`),
        ];

        agentStatsSheet.addRow(agentHeaders);

        const agentHeaderRow = agentStatsSheet.getRow(1);
        agentHeaderRow.font = { bold: true, color: { argb: 'FFFFFF' } };
        agentHeaderRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '4472C4' },
        };
        agentHeaderRow.alignment = { horizontal: 'center', vertical: 'middle' };

        Object.values(agentStats).forEach((stats) => {
          const row = [
            stats.agentName,
            stats.agentId,
            stats.calls.length,
            stats.validDurationCount > 0 ? (stats.totalDuration / stats.validDurationCount).toFixed(2) : '0',
            stats.validPointCount > 0 ? (stats.totalPoint / stats.validPointCount).toFixed(2) : '0',
            stats.validMaxSlienceCount > 0 ? (stats.totalMaxSlience / stats.validMaxSlienceCount).toFixed(2) : '0',
          ];

          limitedQualityCriteria.forEach((criterionName) => {
            const criteriaData = stats.criteriaStats[criterionName];
            if (criteriaData && criteriaData.total > 0) {
              const successRate = (criteriaData.success / criteriaData.total) * 100;
              row.push(`%${successRate.toFixed(1)} (${criteriaData.success}/${criteriaData.total})`);
            } else {
              row.push('Veri Yok');
            }
          });

          agentStatsSheet.addRow(row);
        });

        agentStatsSheet.columns.forEach((column, index) => {
          if (index < 6) {
            column.width = 30;
          } else {
            column.width = 50;
          }
        });

        // Style
        agentStatsSheet.eachRow((row, rowNumber) => {
          if (rowNumber > 1) {
            row.height = 25;

            if (rowNumber % 2 === 0) {
              row.eachCell((cell) => {
                // cell.fill = {
                //   type: 'pattern',
                //   pattern: 'solid',
                //   fgColor: { argb: 'FFF8F9FA' },
                // };
              });
            }

            row.eachCell((cell) => {
              cell.border = {
                top: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                bottom: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                left: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                right: { style: 'thin', color: { argb: 'FFCCCCCC' } },
              };
              cell.alignment = { horizontal: 'center', vertical: 'middle' };
            });

            const pointCell = row.getCell(5);
            const pointValue = parseFloat(pointCell.value);
            // if (!isNaN(pointValue)) {
            //   if (pointValue >= 80) {
            //     pointCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6FFE6' } };
            //     pointCell.font = { color: { argb: 'FF006400' }, bold: true };
            //   } else if (pointValue >= 60) {
            //     pointCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFF0E6' } };
            //     pointCell.font = { color: { argb: 'FF8B4513' }, bold: true };
            //   } else {
            //     pointCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFE6E6' } };
            //     pointCell.font = { color: { argb: 'FF8B0000' }, bold: true };
            //   }
            // }

            // for (let col = 7; col <= agentHeaders.length; col++) {
            //   const cell = row.getCell(col);
            //   const cellValue = cell.value;
            //   if (cellValue && typeof cellValue === 'string' && cellValue.startsWith('%')) {
            //     const percentage = parseFloat(cellValue.match(/\d+\.?\d*/)[0]);
            //     if (!isNaN(percentage)) {
            //       if (percentage >= 80) {
            //         cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6FFE6' } };
            //         cell.font = { color: { argb: 'FF006400' }, bold: true };
            //       }  else {
            //         cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFE6E6' } };
            //         cell.font = { color: { argb: 'FF8B0000' }, bold: true };
            //       }
            //     }
            //   }
            // }
          }
        });

        const agentSummaryRow = agentStatsSheet.getRow(Object.keys(agentStats).length + 3);
        agentSummaryRow.getCell(1).value = 'GENEL ORTALAMA';
        agentSummaryRow.getCell(1).font = { bold: true, size: 12, color: { argb: 'FF0066CC' } };
        agentSummaryRow.getCell(1).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE6F3FF' },
        };

        const totalCalls = Object.values(agentStats).reduce((sum, stats) => sum + stats.calls.length, 0);
        const totalAgents = Object.keys(agentStats).length;
        const overallAvgDuration =
          Object.values(agentStats).reduce(
            (sum, stats) => sum + (stats.validDurationCount > 0 ? stats.totalDuration / stats.validDurationCount : 0),
            0
          ) / totalAgents;
        const overallAvgPoint =
          Object.values(agentStats).reduce(
            (sum, stats) => sum + (stats.validPointCount > 0 ? stats.totalPoint / stats.validPointCount : 0),
            0
          ) / totalAgents;

        agentSummaryRow.getCell(2).value = `${totalAgents} Temsilci`;
        agentSummaryRow.getCell(3).value = totalCalls;
        agentSummaryRow.getCell(4).value = overallAvgDuration.toFixed(2);
        agentSummaryRow.getCell(5).value = overallAvgPoint.toFixed(2);

        for (let col = 1; col <= agentHeaders.length; col++) {
          const cell = agentSummaryRow.getCell(col);
          cell.border = {
            top: { style: 'thick', color: { argb: 'FF0066CC' } },
            bottom: { style: 'thick', color: { argb: 'FF0066CC' } },
            left: { style: 'thin', color: { argb: 'FF808080' } },
            right: { style: 'thin', color: { argb: 'FF808080' } },
          };
          cell.font = { bold: true };
          // cell.fill = {
          //   type: 'pattern',
          //   pattern: 'solid',
          //   fgColor: { argb: 'FFE6F3FF' },
          // };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        }

        worksheet.state = 'visible';
        agentStatsSheet.state = 'visible';
        // #regionend Agent Statistics

        // #region Weekly Call Analysis
        const weeklyAnalysisSheet = workbook.addWorksheet('Haftalık Çağrı Analizi');

        // Group data by weeks
        const getWeekStartDate = (date) => {
          const d = new Date(date);
          const day = d.getDay();
          const diff = d.getDate() - day + (day === 0 ? -6 : 1);
          const monday = new Date(d.setDate(diff));
          monday.setHours(0, 0, 0, 0);
          return monday;
        };

        const weekGroups = {};
        dataToExport.forEach((item) => {
          if (item.date) {
            const itemDate = new Date(item.date);
            const weekStart = getWeekStartDate(itemDate);
            const weekKey = weekStart.toISOString().split('T')[0];

            if (!weekGroups[weekKey]) {
              weekGroups[weekKey] = {
                weekStart: weekStart,
                calls: [],
              };
            }
            weekGroups[weekKey].calls.push(item);
          }
        });

        const dayNames = ['Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi', 'Pazar'];
        const hours = Array.from({ length: 24 }, (_, i) => {
          const startHour = String(i).padStart(2, '0') + ':00';
          const endHour = String((i + 1) % 24).padStart(2, '0') + ':00';
          return {
            key: String(i).padStart(2, '0') + ':00',
            display: `${startHour}-${endHour}`,
          };
        });

        let currentRow = 1;

        const sortedWeeks = Object.entries(weekGroups).sort(([a], [b]) => new Date(a) - new Date(b));

        sortedWeeks.forEach(([weekKey, weekData], weekIndex) => {
          const weekStartDate = weekData.weekStart;
          const weekEndDate = new Date(weekStartDate);
          weekEndDate.setDate(weekStartDate.getDate() + 6);

          const weekTitleRow = weeklyAnalysisSheet.getRow(currentRow);
          weekTitleRow.getCell(1).value = `${weekStartDate.toLocaleDateString(
            'tr-TR'
          )} - ${weekEndDate.toLocaleDateString('tr-TR')} Haftası`;
          weekTitleRow.getCell(1).font = { bold: true, size: 14, color: { argb: 'FF0066CC' } };
          weekTitleRow.getCell(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6F3FF' },
          };
          currentRow += 2;

          // Create hour/day matrix for this week
          const weekMatrix = {};
          dayNames.forEach((day) => {
            weekMatrix[day] = {};
            hours.forEach((hour) => {
              weekMatrix[day][hour.key] = {
                callCount: 0,
                totalPoint: 0,
                validPointCount: 0,
              };
            });
          });

          weekData.calls.forEach((call) => {
            const callDate = new Date(call.date);
            const dayOfWeek = callDate.getDay();
            const dayName = dayNames[dayOfWeek === 0 ? 6 : dayOfWeek - 1];
            const hour = String(callDate.getHours()).padStart(2, '0') + ':00';

            if (weekMatrix[dayName] && weekMatrix[dayName][hour]) {
              weekMatrix[dayName][hour].callCount++;

              if (call.info && call.info.point !== undefined && call.info.point !== null && !isNaN(call.info.point)) {
                weekMatrix[dayName][hour].totalPoint += Number(call.info.point);
                weekMatrix[dayName][hour].validPointCount++;
              }
            }
          });

          const headerRow = weeklyAnalysisSheet.getRow(currentRow);
          headerRow.getCell(1).value = 'Gün / Saat';
          headerRow.getCell(1).font = { bold: true, color: { argb: 'FFFFFF' } };
          headerRow.getCell(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '4472C4' },
          };
          headerRow.getCell(1).alignment = { horizontal: 'center', vertical: 'middle' };

          hours.forEach((hour, hourIndex) => {
            const cell = headerRow.getCell(hourIndex + 2);
            cell.value = hour.display;
            cell.font = { bold: true, color: { argb: 'FFFFFF' } };
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: '4472C4' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
          });

          currentRow++;

          dayNames.forEach((day, dayIndex) => {
            const dayRow = weeklyAnalysisSheet.getRow(currentRow);

            const dailyTotalCalls = Object.values(weekMatrix[day]).reduce(
              (sum, hourData) => sum + hourData.callCount,
              0
            );
            const dailyTotalPoints = Object.values(weekMatrix[day]).reduce(
              (sum, hourData) => sum + hourData.totalPoint,
              0
            );
            const dailyValidPointCount = Object.values(weekMatrix[day]).reduce(
              (sum, hourData) => sum + hourData.validPointCount,
              0
            );
            const dailyAvgPoint = dailyValidPointCount > 0 ? dailyTotalPoints / dailyValidPointCount : 0;

            const dayCell = dayRow.getCell(1);
            dayCell.value = {
              richText: [
                {
                  text: day,
                  font: { bold: true, color: { argb: 'FFFFFF' }, size: 12 },
                },
                {
                  text: '\n(',
                  font: { bold: false, color: { argb: 'FFFFFF' }, size: 10 },
                },
                {
                  text: `${dailyTotalCalls} çağrı - ${dailyAvgPoint.toFixed(1)} puan`,
                  font: { bold: false, color: { argb: 'FFFFFF' }, size: 10 },
                },
                {
                  text: ')',
                  font: { bold: false, color: { argb: 'FFFFFF' }, size: 10 },
                },
              ],
            };

            dayCell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: '5B9BD5' },
            };
            dayCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

            hours.forEach((hour, hourIndex) => {
              const cellData = weekMatrix[day][hour.key];
              const cell = dayRow.getCell(hourIndex + 2);

              const avgPoint = cellData.validPointCount > 0 ? cellData.totalPoint / cellData.validPointCount : 0;

              if (cellData.callCount > 0) {
                cell.value = `(${cellData.callCount} çağrı)\n(${avgPoint.toFixed(1)} puan)`;

                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFFFFFF' },
                };
                cell.font = { color: { argb: 'FF000000' }, bold: true, size: 9 };
              } else {
                cell.value = '-';
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFF5F5F5' },
                };
                cell.font = { color: { argb: 'FF808080' }, size: 10 };
              }

              cell.alignment = {
                horizontal: 'center',
                vertical: 'middle',
                wrapText: true,
              };
              cell.border = {
                top: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                bottom: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                left: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                right: { style: 'thin', color: { argb: 'FFCCCCCC' } },
              };
            });

            currentRow++;
          });

          // Week statistics
          currentRow++;
          const weekStatsRow = weeklyAnalysisSheet.getRow(currentRow);
          weekStatsRow.getCell(1).value = 'Hafta Toplam:';
          weekStatsRow.getCell(1).font = { bold: true };

          const weekTotalCalls = weekData.calls.length;
          const weekTotalPoints = weekData.calls
            .filter((c) => c.info && c.info.point !== undefined)
            .reduce((sum, c) => sum + Number(c.info.point), 0);
          const weekValidPointCount = weekData.calls.filter((c) => c.info && c.info.point !== undefined).length;
          const weekAvgPoint = weekValidPointCount > 0 ? weekTotalPoints / weekValidPointCount : 0;

          weekStatsRow.getCell(2).value = `${weekTotalCalls} çağrı - Ortalama: ${weekAvgPoint.toFixed(1)} puan`;
          weekStatsRow.getCell(2).font = { bold: true, color: { argb: 'FF0066CC' } };

          currentRow += 3;
        });

        weeklyAnalysisSheet.getColumn(1).width = 40;
        for (let i = 2; i <= 25; i++) {
          weeklyAnalysisSheet.getColumn(i).width = 12;
        }
        // #regionend Weekly Call Analysis

        worksheet.state = 'visible';
        agentStatsSheet.state = 'visible';

        const idColumnIndex = visibleColumns.findIndex((col) => col.accessorKey === 'identifier');
        if (idColumnIndex !== -1) {
          dataToExport.forEach((item, rowIndex) => {
            const excelRowIndex = rowIndex + 2;
            const cell = worksheet.getCell(excelRowIndex, idColumnIndex + 2);
            const channelType = entityType;
            const detailUrl = `${window.location.origin}/v2.0/channelsDetail?channelType=${channelType}&id=${item.id}`;
            cell.value = {
              text: item.identifier || item.id,
              hyperlink: detailUrl,
            };
            cell.font = { color: { argb: 'FF0066CC' }, underline: true };
          });
        }

        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${entityText}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);

        setExportModalOpened(false);
      } catch (error) {
        console.error("Excel'e aktarma sırasında hata oluştu:", error);
      } finally {
        setIsLoading(false);
      }
    };

    const table = useMantineReactTable({
      columns: tableColumns
        .filter((column) => !(column?.adminOnly && user?.role !== 'ADMİN'))
        .map((column) => {
          return {
            ...column,
            size: columnWidths[column.accessorKey] || 160,
            minSize: 120,
            maxSize: 300,
            flexGrow: 1,
            flexShrink: 1,
            Header: ({ column }) => (
              <div style={{ whiteSpace: 'normal', textAlign: 'center' }}>{column.columnDef.header}</div>
            ),
          };
        }),

      enableColumnResizing: true,

      renderTopToolbarCustomActions: ({ table }) => (
        <div style={{ display: 'flex', alignItems: 'center', width: '100%', height: '100%' }}>
          <Button style={{ marginLeft: 'auto' }} size="xs" color="gray" variant="outline" onClick={resetColumnWidths}>
            Sütunları Sıfırla
          </Button>
          {tableButtons.map((x, i) => (
            <Button key={i} ms="md" size="xs" color="gray" variant="outline" onClick={x.onClick}>
              {x.text}
            </Button>
          ))}
          {selectedItemActionButtons.length > 0 ? (
            <Flex gap="xs">
              {selectedItemActionButtons.map((selectedItemActionButton, index) => {
                let selectedRowIds = Object.keys(rowSelection).map(Number);
                return (
                  selectedItemActionButton.isVisible ? selectedItemActionButton.isVisible(selectedRowIds) : true
                ) ? (
                  <Button
                    key={index}
                    ms="md"
                    size="xs"
                    color="blue"
                    variant="outline"
                    disabled={Object.keys(rowSelection).length === 0}
                    onClick={() => {
                      selectedItemActionButton.onClick(selectedRowIds);
                    }}
                  >
                    {selectedItemActionButton.text}
                  </Button>
                ) : null;
              })}
            </Flex>
          ) : null}
          {allowAdd ? (
            <Button
              ms="md"
              color="teal"
              size="xs"
              leftSection={<IconPlus />}
              onClick={async () => {
                setMode('add');
                tableForm.reset();
                tableForm.setValues(await entityToAdd());
                open();
              }}
              variant="filled"
            >
              Yeni {entityText} Ekle
            </Button>
          ) : null}
          <Button
            ms="md"
            color="blue"
            size="xs"
            leftSection={<IconFileExport />}
            onClick={() => setExportModalOpened(true)}
            variant="filled"
          >
            Excel&apos;e Aktar
          </Button>
        </div>
      ),

      renderToolbarInternalActions: ({ table }) => (
        <Flex gap="xs" align="center">
          {columnFilters.length > 0 && (
            <Tooltip label="Filtreleri Temizle">
              <ActionIcon
                variant="subtle"
                color="gray"
                onClick={() => {
                  setColumnFilters(initColumnFilters);
                }}
              >
                <IconFilterCancel />
              </ActionIcon>
            </Tooltip>
          )}
          {topIconButtons.map((x) => (
            <>
              <Tooltip label={x.text} position="bottom" offset={5}>
                <ActionIcon variant="subtle" color="gray" onClick={x.onClick}>
                  {x.icon}
                </ActionIcon>
              </Tooltip>
            </>
          ))}
          <Tooltip label="Yenile">
            <ActionIcon variant="subtle" color="gray" onClick={fetchData}>
              <IconRefresh />
            </ActionIcon>
          </Tooltip>
          <MRT_ShowHideColumnsButton table={table} />
          <MRT_ToggleDensePaddingButton table={table} />
        </Flex>
      ),

      data,
      mantinePaginationProps: {
        rowsPerPageOptions: ['20', '100', '200', '500', '1000'],
        withEdges: false,
      },

      mantineTableBodyRowProps: ({ row }) => ({
        onClick: (e) => {
          if (stopPropagationSelector && e.target.closest(stopPropagationSelector)) {
            return;
          }
          onRowClick(row.original, row);
        },
        style: { cursor: 'pointer' },
      }),
      enableColumnFilters: true,
      getRowId: (row) => row.id,
      enableDensityToggle: false,
      enableGlobalFilterModes: false,
      enableFullScreenToggle: false,
      enableGlobalFilter: false,
      enableGrouping: grouping.length > 0 ? true : false,
      initialState: {
        grouping: grouping,
        sorting: initSorting,
        showColumnFilters: false,
        density: 'xs',
        showGlobalFilter: false,
        columnVisibility: hidedDefaultColumns,
      },
      manualFiltering: true,
      manualPagination: true,
      manualSorting: true,
      localization: MRT_Localization_TR,
      rowCount,
      enableMultiSort: true,
      enableStickyHeader: true,
      mantineTableContainerProps: { style: { maxHeight: '900px' } },
      enableStickyFooter: true,
      onColumnVisibilityChange: setColumnVisibility,
      onColumnFiltersChange: (updater) => {
        const newFilters = typeof updater === 'function' ? updater(columnFilters) : updater;
        setColumnFilters(newFilters);
        setRowSelection({});
      },
      onPaginationChange: (updater) => {
        const newPagination = typeof updater === 'function' ? updater(pagination) : updater;
        setPagination(newPagination);
        setRowSelection({});
      },
      onSortingChange: (updater) => {
        const newSorting = typeof updater === 'function' ? updater(sorting) : updater;
        setSorting(newSorting);
        setRowSelection({});
      },
      onShowColumnFiltersChange: setShowColumnFilters,
      enableRowActions: true,
      enableRowSelection: true,
      onRowSelectionChange: setRowSelection,

      renderRowActionMenuItems: ({ row }) => (
        <>
          {actionButtons.map((actionButton, index) =>
            (actionButton.isVisible ? actionButton.isVisible(row.original) : true) ? (
              <Menu.Item key={index} onClick={() => actionButton.onClick(row.original)}>
                {actionButton.text}
              </Menu.Item>
            ) : (
              <></>
            )
          )}
          {allowUpdate(row.original) && (
            <Menu.Item
              onClick={() => {
                (async () => {
                  setMode('update');
                  const values = await entityToUpdate(row.original);
                  tableForm.setValues(values);
                  open();
                })();
              }}
            >
              Güncelle
            </Menu.Item>
          )}
          {allowDelete && <Menu.Item onClick={() => handleDelete(row.original.id)}>Sil</Menu.Item>}
        </>
      ),
      state: {
        rowSelection,
        columnFilters,
        columnVisibility,
        isLoading,
        pagination,
        showColumnFilters,
        showAlertBanner: isError,
        showProgressBars: isRefetching,
        sorting,
      },
      mantineToolbarAlertBannerProps: isError ? { color: 'red', children: 'Veri yüklenirken hata oluştu!' } : undefined,
    });
    return (
      <>
        <MantineReactTable table={table} />
        <Drawer
          opened={opened}
          onClose={close}
          title={mode === 'add' ? 'Yeni ' + entityText + ' Ekle' : entityText + ' Güncelle'}
        >
          <form onSubmit={handleTableFormSubmit}>
            {form && form(mode, tableForm)}
            <Button type="submit" fullWidth mt="md">
              {mode === 'add' ? 'Ekle' : 'Güncelle'}
            </Button>
            <FormError errorText={tableForm.errors['formError']} />
          </form>
        </Drawer>

        {/* Excel'e Aktarma Modalı */}
        <Modal
          opened={exportModalOpened}
          onClose={() => setExportModalOpened(false)}
          title={<Title order={4}>Excel&apos;e Aktarma</Title>}
          size="md"
          padding="lg"
          centered
        >
          {isLoading ? (
            <Flex direction="column" align="center" gap="md" p="xl">
              <Loader size="lg" variant="dots" />
              <Text size="sm" fw={500}>
                Excel dosyası hazırlanıyor, lütfen bekleyin...
              </Text>
            </Flex>
          ) : (
            <Flex direction="column" gap="md">
              <Paper p="md" withBorder shadow="xs" radius="sm">
                <Title order={5} mb="xs" c="blue.7">
                  Veri Kapsamı
                </Title>
                <Divider mb="sm" />
                <Flex direction="column" gap="sm">
                  <Button
                    onClick={() => exportToExcel(false)}
                    variant="outline"
                    color="blue"
                    leftSection={<IconFileExport size={16} />}
                  >
                    Tabloda görünen verileri aktar{' '}
                    <Badge ml="xs" variant="light">
                      {data.length} kayıt
                    </Badge>
                  </Button>
                  <Button
                    onClick={() => exportToExcel(true)}
                    variant="filled"
                    color="blue"
                    leftSection={<IconFileExport size={16} />}
                  >
                    Filtrelenmiş tüm verileri aktar{' '}
                    <Badge ml="xs" variant="filled">
                      {rowCount} kayıt
                    </Badge>
                  </Button>

                  <Flex gap="sm" align="center">
                    <NumberInput
                      placeholder="Kayıt sayısı"
                      min={1}
                      max={100000}
                      defaultValue={1000}
                      id="customRecordCount"
                      style={{ flex: 1 }}
                      hideControls
                    />
                    <Button
                      onClick={() => {
                        const inputElement = document.getElementById('customRecordCount');
                        const count = parseInt(inputElement?.value || '1000');
                        exportToExcel(true, count);
                      }}
                      variant="light"
                      color="blue"
                      leftSection={<IconFileExport size={16} />}
                      style={{ flex: 2 }}
                    >
                      Belirli sayıda veri aktar
                    </Button>
                  </Flex>
                </Flex>
              </Paper>

              <Paper p="md" withBorder shadow="xs" radius="sm">
                <Title order={5} mb="xs" c="blue.7">
                  Sütun Seçimi
                </Title>
                <Divider mb="sm" />

                <Group mb="md">
                  <Checkbox
                    label="Tümünü Seç/Kaldır"
                    checked={
                      exportColumns.length ===
                      tableColumns.filter(
                        (col) => !(col?.adminOnly && user?.role !== 'ADMİN') && !columnVisibility[col.accessorKey]
                      ).length
                    }
                    indeterminate={
                      exportColumns.length > 0 &&
                      exportColumns.length <
                        tableColumns.filter(
                          (col) => !(col?.adminOnly && user?.role !== 'ADMİN') && !columnVisibility[col.accessorKey]
                        ).length
                    }
                    onChange={(e) => {
                      if (e.currentTarget.checked) {
                        setExportColumns(
                          tableColumns
                            .filter(
                              (col) => !(col?.adminOnly && user?.role !== 'ADMİN') && !columnVisibility[col.accessorKey]
                            )
                            .map((col) => col.accessorKey)
                        );
                      } else {
                        setExportColumns([]);
                      }
                    }}
                  />
                  <Badge>{exportColumns.length} sütun seçili</Badge>
                </Group>

                <ScrollArea style={{ height: 200 }} scrollbarSize={6} offsetScrollbars>
                  <Box p="xs" style={{ border: '1px solid #eee', borderRadius: '4px', background: '#f9f9f9' }}>
                    {tableColumns
                      .filter(
                        (col) => !(col?.adminOnly && user?.role !== 'ADMİN') && !columnVisibility[col.accessorKey]
                      )
                      .map((column, index) => (
                        <Checkbox
                          key={column.accessorKey}
                          label={column.header}
                          checked={exportColumns.includes(column.accessorKey)}
                          styles={{
                            root: {
                              padding: '6px 8px',
                              marginBottom: '2px',
                              borderRadius: '4px',
                              backgroundColor: exportColumns.includes(column.accessorKey) ? '#e6f7ff' : 'transparent',
                              '&:hover': { backgroundColor: '#f0f0f0' },
                            },
                          }}
                          onChange={(e) => {
                            if (e.currentTarget.checked) {
                              setExportColumns([...exportColumns, column.accessorKey]);
                            } else {
                              setExportColumns(exportColumns.filter((key) => key !== column.accessorKey));
                            }
                          }}
                        />
                      ))}
                  </Box>
                </ScrollArea>
              </Paper>

              <Paper p="md" withBorder shadow="xs" radius="sm" bg="blue.0">
                <Flex align="center" gap="md">
                  <IconFileExport size={20} stroke={1.5} color="#228be6" />
                  <Text size="sm" color="dimmed">
                    Excel&apos;e aktarma işlemi mevcut filtrelerinize göre yapılacaktır.
                    {columnFilters.length > 0 ? (
                      <Text component="span" fw={500} c="blue.7">
                        {' '}
                        Tablonuzda {columnFilters.length} aktif filtre bulunmaktadır.
                      </Text>
                    ) : (
                      ' Tablonuzda filtre bulunmamaktadır.'
                    )}
                  </Text>
                </Flex>
              </Paper>
            </Flex>
          )}
        </Modal>
      </>
    );
  }
);
