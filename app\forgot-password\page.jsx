'use client';
import React, { useContext, useEffect, useRef } from 'react';
import { useState } from 'react';
import { useForm } from '@mantine/form';
import { PinInput } from '@mantine/core';
import { FormError } from '@/common/components/FormError';
import Link from 'next/link';
import { AuthContext } from '@/common/contexts/AuthContext';

const ForgotPassword = () => {
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const { fetchAuthClient } = useContext(AuthContext);

  const [timer, setTimer] = useState(300);
  const timerRef = useRef();
  const [codeExpired, setCodeExpired] = useState(false);

  useEffect(() => {
    if (step === 2 && timer > 0 && !codeExpired) {
      timerRef.current = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            clearInterval(timerRef.current);
            setCodeExpired(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timerRef.current);
    } else if (step !== 2) {
      setTimer(300);
      setCodeExpired(false);
      clearInterval(timerRef.current);
    }
  }, [step]);

  const emailForm = useForm({
    mode: 'controlled',
    initialValues: { email: '' },
  });

  const verificationForm = useForm({
    mode: 'controlled',
    initialValues: { verificationCode: '' },
  });

  const passwordForm = useForm({
    mode: 'controlled',
    initialValues: { password: '', confirmPassword: '' },
    validate: {
      confirmPassword: (value, values) => (value !== values.password ? 'Şifreler eşleşmiyor' : null),
    },
  });

  const handleEmailSubmit = async (e) => {
    e.preventDefault();
    emailForm.clearErrors();
    try {
      const response = await fetchAuthClient('Auth/forgotPassword/sendCode', {
        method: 'POST',
        body: (() => {
          const fd = new FormData();
          fd.append('email', emailForm.values.email);
          return fd;
        })(),
      });
      if (!response.ok) {
        const data = await response.json().catch(() => ({}));
        emailForm.setErrors({ formError: data.message || data.error || 'Bir sorun oluştu.' });
        return;
      }
      setEmail(emailForm.values.email);
      setStep(2);
      setTimer(300);
      setCodeExpired(false);
    } catch (err) {
      let errorMsg = 'Bir sorun oluştu.';
      if (err && err.response) {
        try {
          const data = await err.response.json();
          errorMsg = data.message || data.error || errorMsg;
        } catch {}
      } else if (err && err.message) {
        errorMsg = err.message;
      }
      emailForm.setErrors({ formError: errorMsg });
    }
  };

  const handleVerificationSubmit = async (e) => {
    e.preventDefault();
    verificationForm.clearErrors();
    if (codeExpired) {
      verificationForm.setErrors({ formError: 'Kodun süresi doldu. Lütfen tekrar kod isteyin.' });
      return;
    }
    try {
      const response = await fetchAuthClient('Auth/forgotPassword/verifyCode', {
        method: 'POST',
        body: (() => {
          const fd = new FormData();
          fd.append('email', email);
          fd.append('code', verificationCode);
          return fd;
        })(),
      });
      if (!response.ok) {
        const data = await response.json().catch(() => ({}));
        verificationForm.setErrors({ formError: data.message || data.error || 'Geçersiz doğrulama kodu. Lütfen tekrar deneyin.' });
        return;
      }
      setStep(3);
    } catch (err) {
      let errorMsg = 'Bir sorun oluştu.';
      if (err && err.response) {
        try {
          const data = await err.response.json();
          errorMsg = data.message || data.error || errorMsg;
        } catch {}
      } else if (err && err.message) {
        errorMsg = err.message;
      }
      verificationForm.setErrors({ formError: errorMsg });
    }
  };

  const handlePasswordResetSubmit = async (e) => {
    e.preventDefault();
    passwordForm.clearErrors();
    try {
      const response = await fetchAuthClient('Auth/forgotPassword/changePasswordWithCode', {
        method: 'POST',
        body: (() => {
          const fd = new FormData();
          fd.append('email', email);
          fd.append('code', verificationCode);
          fd.append('password', passwordForm.values.password);
          return fd;
        })(),
      });
      if (!response.ok) {
        const data = await response.json().catch(() => ({}));
        passwordForm.setErrors({ formError: data.message || data.error || 'Şifre sıfırlama işlemi başarısız oldu. Lütfen tekrar deneyin.' });
        return;
      }
      window.location.href = '/login';
    } catch (err) {
      let errorMsg = 'Bir sorun oluştu.';
      if (err && err.response) {
        try {
          const data = await err.response.json();
          errorMsg = data.message || data.error || errorMsg;
        } catch {}
      } else if (err && err.message) {
        errorMsg = err.message;
      }
      passwordForm.setErrors({ formError: errorMsg });
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <form className="w-full max-w-md space-y-5" onSubmit={handleEmailSubmit}>
            <div className="group">
              <label
                className="block text-gray-700 font-semibold mb-1 text-base group-focus-within:text-green-600 transition-colors duration-200"
                htmlFor="email"
              >
                E-Posta
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400 group-focus-within:text-green-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                    />
                  </svg>
                </div>
                <input
                  id="email"
                  type="email"
                  autoComplete="email"
                  {...emailForm.getInputProps('email')}
                  className="w-full pl-12 pr-4 py-3 rounded-xl bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 font-medium shadow-sm border border-gray-200 hover:border-green-200 transition-all duration-200"
                  placeholder="E-posta adresinizi girin"
                  required
                />
              </div>
            </div>

            <button
              type="submit"
              className="w-full py-3 rounded-xl bg-gradient-to-r from-green-500 to-green-600 text-white font-bold text-lg shadow-lg hover:shadow-green-200/50 hover:brightness-105 hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
            >
              Doğrulama Kodu Gönder
            </button>

            <FormError errorText={emailForm.errors['formError']} />

            <div className="text-center mt-4">
              <Link href="/login" className="text-green-600 hover:text-green-700 text-sm font-medium">
                Giriş sayfasına dön
              </Link>
            </div>
          </form>
        );

      case 2:
        return (
          <form className="w-full max-w-md space-y-5" onSubmit={handleVerificationSubmit}>
            <div className="text-center mb-4">
              <p className="text-gray-600 mb-2">
                <span className="font-semibold">{email}</span> adresine doğrulama kodu gönderdik.
              </p>
              <p className="text-gray-600 text-sm">Lütfen e-posta kutunuzu kontrol edin ve aldığınız kodu girin.</p>
            </div>

            <div className="group">
              <label
                className="block text-gray-700 font-semibold mb-1 text-base group-focus-within:text-green-600 transition-colors duration-200"
                htmlFor="verificationCode"
              >
                Doğrulama Kodu
              </label>
              <div className="flex justify-center mt-3">
                <PinInput
                  id="verificationCode"
                  length={6}
                  value={verificationCode}
                  onChange={setVerificationCode}
                  type="number"
                  placeholder=""
                  size="md"
                  styles={{
                    input: {
                      borderColor: '#e5e7eb',
                      color: '#111827',
                      fontWeight: 500,
                      '&:focus': {
                        borderColor: '#22c55e',
                        boxShadow: '0 0 0 2px rgba(34, 197, 94, 0.2)',
                      },
                      '&:hover': {
                        borderColor: '#bbf7d0',
                      },
                    },
                  }}
                  required
                  disabled={codeExpired}
                />
              </div>
              <div className="flex items-center justify-between mt-3">
                <div className="text-xs text-gray-500">
                  {codeExpired ? (
                    <span className="text-red-500 font-semibold">Kodun süresi doldu</span>
                  ) : (
                    <span>Kodun geçerlilik süresi: <span className="font-semibold">{Math.floor(timer/60)}:{(timer%60).toString().padStart(2,'0')}</span></span>
                  )}
                </div>
              </div>
            </div>

            <button
              type="submit"
              className="w-full py-3 rounded-xl bg-gradient-to-r from-green-500 to-green-600 text-white font-bold text-lg shadow-lg hover:shadow-green-200/50 hover:brightness-105 hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
              disabled={verificationCode.length !== 6 || codeExpired}
            >
              Doğrula
            </button>

            <FormError errorText={verificationForm.errors['formError']} />

            <div className="text-center mt-4 flex justify-between text-sm">
              <button
                type="button"
                onClick={() => setStep(1)}
                className="text-gray-500 hover:text-gray-700 font-medium"
              >
                Geri Dön
              </button>
              <button
                type="button"
                onClick={async () => {
                  setTimer(300);
                  setCodeExpired(false);
                  setVerificationCode('');
                  verificationForm.clearErrors();
                  await handleEmailSubmit({ preventDefault: () => {} });
                }}
                className="text-green-600 hover:text-green-700 font-medium"
                disabled={!codeExpired && timer > 0}
              >
                Kodu Tekrar Gönder
              </button>
            </div>
          </form>
        );

      case 3:
        return (
          <form className="w-full max-w-md space-y-5" onSubmit={handlePasswordResetSubmit}>
            <div className="text-center mb-4">
              <p className="text-gray-600">Şimdi yeni şifrenizi belirleyebilirsiniz.</p>
            </div>

            <div className="group">
              <label
                className="block text-gray-700 font-semibold mb-1 text-base group-focus-within:text-green-600 transition-colors duration-200"
                htmlFor="password"
              >
                Yeni Şifre
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400 group-focus-within:text-green-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                </div>
                <input
                  id="password"
                  type="password"
                  {...passwordForm.getInputProps('password')}
                  className="w-full pl-12 pr-4 py-3 rounded-xl bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 font-medium shadow-sm border border-gray-200 hover:border-green-200 transition-all duration-200"
                  placeholder="Yeni şifrenizi girin"
                  required
                />
              </div>
            </div>

            <div className="group">
              <label
                className="block text-gray-700 font-semibold mb-1 text-base group-focus-within:text-green-600 transition-colors duration-200"
                htmlFor="confirmPassword"
              >
                Şifre Tekrar
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400 group-focus-within:text-green-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                </div>
                <input
                  id="confirmPassword"
                  type="password"
                  {...passwordForm.getInputProps('confirmPassword')}
                  className="w-full pl-12 pr-4 py-3 rounded-xl bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 font-medium shadow-sm border border-gray-200 hover:border-green-200 transition-all duration-200"
                  placeholder="Şifrenizi tekrar girin"
                  required
                />
              </div>
              {passwordForm.errors.confirmPassword && (
                <p className="text-red-500 text-sm mt-1">{passwordForm.errors.confirmPassword}</p>
              )}
            </div>

            <button
              type="submit"
              className="w-full py-3 rounded-xl bg-gradient-to-r from-green-500 to-green-600 text-white font-bold text-lg shadow-lg hover:shadow-green-200/50 hover:brightness-105 hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
            >
              Şifremi Yenile
            </button>

            <FormError errorText={passwordForm.errors['formError']} />

            <div className="text-center mt-4">
              <button
                type="button"
                onClick={() => setStep(2)}
                className="text-gray-500 hover:text-gray-700 text-sm font-medium"
              >
                Geri Dön
              </button>
            </div>
          </form>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gradient-to-br from-green-50 via-white to-green-100 relative overflow-hidden py-10">
      <div className="absolute top-0 left-0 w-[500px] h-[500px] bg-green-300 opacity-20 rounded-full blur-3xl -z-10 animate-pulse"></div>
      <div className="absolute bottom-0 right-0 w-[500px] h-[500px] bg-green-200 opacity-20 rounded-full blur-3xl -z-10 animate-pulse"></div>
      <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-green-400 opacity-10 rounded-full blur-3xl -z-10 animate-pulse delay-700"></div>
      <div className="absolute bottom-1/3 left-1/3 w-96 h-96 bg-green-100 opacity-30 rounded-full blur-3xl -z-10 animate-pulse delay-1000"></div>

      <div className="absolute top-10 left-10 w-6 h-6 border-t-4 border-l-4 border-green-400 opacity-60"></div>
      <div className="absolute bottom-10 right-10 w-6 h-6 border-b-4 border-r-4 border-green-400 opacity-60"></div>
      <div className="absolute top-10 right-10 w-4 h-4 bg-green-400 rounded-full opacity-60"></div>
      <div className="absolute bottom-10 left-10 w-4 h-4 bg-green-400 rounded-full opacity-60"></div>

      <div className="max-w-5xl w-full mx-4 md:mx-auto flex flex-col md:flex-row rounded-3xl shadow-[0_20px_50px_rgba(8,107,46,0.15)] bg-white/95 backdrop-blur-xl border border-green-100/50 overflow-hidden max-h-[90vh] md:max-h-[80vh]">
        {/* Left panel */}
        <div className="hidden md:flex flex-col justify-center relative items-center w-1/2 bg-gradient-to-br from-green-500 via-green-600 to-green-700 p-8 overflow-y-hidden">
          <div className="absolute inset-0 bg-[url('/pattern.svg')] opacity-10"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-transparent"></div>

          <div className="relative z-10 flex flex-col items-center">
            <div className="bg-white/95 rounded-2xl p-4 shadow-xl mb-6 transform hover:scale-105 transition-transform duration-300">
              <img src="/logo.svg" alt="Plukto Logo" className="w-40 h-24 drop-shadow-xl" />
            </div>

            <h1 className="text-4xl font-extrabold text-white mb-4 text-center drop-shadow-lg">
              Şifrenizi mi <span className="text-green-200">Unuttunuz?</span>
            </h1>

            <p className="text-white text-lg text-center font-medium mb-6 leading-relaxed">
              Endişelenmeyin, hesabınıza erişim sağlamanız için size yardımcı olacağız.
              <br />
              <span className="text-green-200 font-bold">Birkaç basit adımla</span> şifrenizi kolayca
              sıfırlayabilirsiniz.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl py-5 px-6 border border-white/20 shadow-lg">
              <ul className="text-white text-base space-y-4">
                <li className="flex items-start">
                  <div className="flex items-center justify-center mr-3 h-6 w-6 rounded-full bg-green-200/20 text-green-200 flex-shrink-0 mt-0.5">
                    1
                  </div>
                  <span>E-posta adresinizi girin</span>
                </li>
                <li className="flex items-start">
                  <div className="flex items-center justify-center mr-3 h-6 w-6 rounded-full bg-green-200/20 text-green-200 flex-shrink-0 mt-0.5">
                    2
                  </div>
                  <span>Size gönderilen doğrulama kodunu kullanın</span>
                </li>
                <li className="flex items-start">
                  <div className="flex items-center justify-center mr-3 h-6 w-6 rounded-full bg-green-200/20 text-green-200 flex-shrink-0 mt-0.5">
                    3
                  </div>
                  <span>Yeni şifrenizi belirleyin ve hesabınıza giriş yapın</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Right panel  */}
        <div className="w-full md:w-1/2 flex flex-col items-center justify-center px-6 py-8 bg-white/95 backdrop-blur-xl overflow-y-auto">
          <div className="md:hidden flex flex-col items-center mb-6">
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-xl shadow-lg mb-4">
              <img src="/logo.svg" alt="Plukto Logo" className="w-20 h-20 drop-shadow-xl bg-white p-2 rounded-lg" />
            </div>
            <h1 className="text-2xl font-extrabold text-green-600 mb-2 text-center drop-shadow-lg">
              Şifrenizi mi Unuttunuz?
            </h1>
          </div>

          <h2 className="text-2xl font-bold text-gray-800 mb-6 hidden md:block">
            {step === 1 && 'Şifre Sıfırlama'}
            {step === 2 && 'Doğrulama Kodu'}
            {step === 3 && 'Yeni Şifre Belirleme'}
          </h2>

          {/* Progress indicator */}
          <div className="flex items-center justify-center w-full max-w-md mb-8">
            <div className="flex items-center w-full">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step >= 1 ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-500'
                }`}
              >
                1
              </div>
              <div className={`h-1 flex-1 mx-2 ${step >= 2 ? 'bg-green-500' : 'bg-gray-200'}`}></div>
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step >= 2 ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-500'
                }`}
              >
                2
              </div>
              <div className={`h-1 flex-1 mx-2 ${step >= 3 ? 'bg-green-500' : 'bg-gray-200'}`}></div>
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step >= 3 ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-500'
                }`}
              >
                3
              </div>
            </div>
          </div>

          {renderStepContent()}

          <div className="mt-6 text-sm text-gray-500 text-center">
            © {new Date().getFullYear()} Plukto. Tüm hakları saklıdır.
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
