'use client';
import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>on, Badge, Card, Button, Modal, Textarea, Text, Group, Paper } from '@mantine/core';
import { IconMessageCircle } from '@tabler/icons-react';

export default function QualityRuleErrors({ detail, permissions = [], channelType = 'Call' }) {
  const [feedbackModalOpen, setFeedbackModalOpen] = useState(false);
  const [selectedCriterion, setSelectedCriterion] = useState(null);
  const [logicFeedback, setLogicFeedback] = useState('');
  const [pointFeedback, setPointFeedback] = useState('');
  const [overallFeedback, setOverallFeedback] = useState('');
  const [feedbackItems, setFeedbackItems] = useState([]);
  const [showFeedbackMode, setShowFeedbackMode] = useState(false);
  
  const hasFeedbackPermission = permissions.includes(`${channelType}.QualityFeedback`);
  // const hasFeedbackPermission = true;

  const totalPoints = detail.analysis.reduce((acc, item) => acc + item.point, 0);
  const totalMaxPoints = detail.analysis.reduce((acc, item) => acc + item.maxPoint, 0);
  const totalPercentage =
    totalMaxPoints > 0 ? ((totalPoints / totalMaxPoints) * 100).toFixed(1) : 'N/A';
  
  const groupedData = detail.analysis.reduce((acc, item) => {
    const { controlCategory, point, maxPoint, isSuccess } = item;
    if (!acc[controlCategory]) {
      acc[controlCategory] = {
        totalPoint: 0,
        totalMaxPoint: 0,
        successfulCriteria: [],
        failedCriteria: [],
      };
    }
    if (isSuccess) {
      acc[controlCategory].successfulCriteria.push(item);
    } else {
      acc[controlCategory].failedCriteria.push(item);
    }
    acc[controlCategory].totalPoint += point;
    acc[controlCategory].totalMaxPoint += maxPoint;
    return acc;
  }, {});
  
  const categoryNames = Object.keys(groupedData).sort(
    (a, b) => groupedData[b].totalMaxPoint - groupedData[a].totalMaxPoint
  );

  const openFeedbackModal = (criterion) => {
    setSelectedCriterion(criterion);
    setLogicFeedback('');
    setPointFeedback('');
    setFeedbackModalOpen(true);
  };

  const handleFeedbackSubmit = () => {
    if (selectedCriterion) {
      const newFeedback = {
        criterionId: selectedCriterion.id || `${selectedCriterion.controlCategory}-${selectedCriterion.controlCriterion}`,
        criterionName: selectedCriterion.controlCriterion,
        category: selectedCriterion.controlCategory,
        logicFeedback,
        pointFeedback,
      };
      
      setFeedbackItems(prev => [...prev.filter(item => 
        item.criterionId !== newFeedback.criterionId), newFeedback]);
    }
    
    setFeedbackModalOpen(false);
  };

  const submitAllFeedback = () => {
    const feedbackData = {
      overallFeedback,
      criteriaFeedback: feedbackItems,
      channelId: detail.dto?.info?.id || 'unknown',
      timestamp: new Date().toISOString()
    };
    
    console.log('Submitting feedback:', JSON.stringify(feedbackData, null, 2));
    
    setFeedbackItems([]);
    setOverallFeedback('');
    setShowFeedbackMode(false);
  };

  const renderCriterionCard = (criterion, index, isSuccess) => {
    const hasFeedback = feedbackItems.some(
      item => item.criterionId === (criterion.id || `${criterion.controlCategory}-${criterion.controlCriterion}`)
    );
    
    return (
      <div
        key={index}
        style={{
          display: 'flex',
          flexDirection: 'column',
          marginLeft: '20px',
          marginTop: '8px',
          paddingRight: '20px',
        }}
      >
        <Card
          shadow="sm"
          padding="lg"
          radius="md"
          withBorder
          style={{ 
            borderLeft: `4px solid ${isSuccess ? '#4caf50' : '#f44336'}`,
            position: 'relative'
          }}
        >
          <div style={{ display: 'flex' }}>
            <span style={{ fontWeight: 'bold' }}>
              {criterion.controlCriterion}
            </span>
            <span style={{ marginLeft: 'auto' }}>
              {criterion.point}/{criterion.maxPoint} Puan{' '}
              <strong style={{ color: isSuccess ? '#4caf50' : '#f44336' }}>
                {isSuccess ? 'Başarılı' : 'Başarısız'}
              </strong>
            </span>
          </div>
          <div>
            <em>{criterion.description}</em>
          </div>
          {showFeedbackMode && hasFeedbackPermission && (
            <div style={{ marginTop: '10px', textAlign: 'right' }}>
              <Button 
                variant={hasFeedback ? "filled" : "outline"} 
                color={hasFeedback ? "blue" : "gray"} 
                size="xs" 
                onClick={() => openFeedbackModal(criterion)}
              >
                {hasFeedback ? 'Geri Bildirim Verildi ✓' : 'Sorun Bildir'}
              </Button>
            </div>
          )}
        </Card>
      </div>
    );
  };

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '15px' }}>
        <div>
          <span>Genel Toplam Puan</span>
          &nbsp;–&nbsp;
          <b>{detail.dto.info.point} / 100 Puan</b>
          <span style={{ marginLeft: '8px' }}>({totalPercentage}%)</span>
        </div>
        
        {hasFeedbackPermission && (
          <Button
            leftIcon={<IconMessageCircle size={16} />}
            color={showFeedbackMode ? "red" : "blue"}
            onClick={() => setShowFeedbackMode(!showFeedbackMode)}
          >
            {showFeedbackMode ? "Geri Bildirim Modunu Kapat" : "Geri Bildirim Yap"}
          </Button>
        )}
      </div>

      <Accordion variant="contained" chevronPosition="left" multiple defaultValue={Object.keys(
        detail.analysis.reduce((acc, item) => {
          if (!acc[item.controlCategory]) {
            acc[item.controlCategory] = true;
          }
          return acc;
        }, {})
      )}>
        {Object.keys(
          detail.analysis.reduce((acc, item) => {
            const { controlCategory, point, maxPoint, isSuccess } = item;
            if (!acc[controlCategory]) {
              acc[controlCategory] = {
                totalPoint: 0,
                totalMaxPoint: 0,
                successfulCriteria: [],
                failedCriteria: [],
              };
            }
            if (isSuccess) {
              acc[controlCategory].successfulCriteria.push(item);
            } else {
              acc[controlCategory].failedCriteria.push(item);
            }
            acc[controlCategory].totalPoint += point;
            acc[controlCategory].totalMaxPoint += maxPoint;
            return acc;
          }, {})
        ).sort(
          (a, b) => {
            const groupedData = detail.analysis.reduce((acc, item) => {
              const { controlCategory, point, maxPoint, isSuccess } = item;
              if (!acc[controlCategory]) {
                acc[controlCategory] = {
                  totalPoint: 0,
                  totalMaxPoint: 0,
                  successfulCriteria: [],
                  failedCriteria: [],
                };
              }
              if (isSuccess) {
                acc[controlCategory].successfulCriteria.push(item);
              } else {
                acc[controlCategory].failedCriteria.push(item);
              }
              acc[controlCategory].totalPoint += point;
              acc[controlCategory].totalMaxPoint += maxPoint;
              return acc;
            }, {});
            return groupedData[b].totalMaxPoint - groupedData[a].totalMaxPoint;
          }
        ).map((categoryName) => {
          const groupedData = detail.analysis.reduce((acc, item) => {
            const { controlCategory, point, maxPoint, isSuccess } = item;
            if (!acc[controlCategory]) {
              acc[controlCategory] = {
                totalPoint: 0,
                totalMaxPoint: 0,
                successfulCriteria: [],
                failedCriteria: [],
              };
            }
            if (isSuccess) {
              acc[controlCategory].successfulCriteria.push(item);
            } else {
              acc[controlCategory].failedCriteria.push(item);
            }
            acc[controlCategory].totalPoint += point;
            acc[controlCategory].totalMaxPoint += maxPoint;
            return acc;
          }, {});
          const { totalPoint, totalMaxPoint, successfulCriteria, failedCriteria } =
            groupedData[categoryName];
          const percentage =
            totalMaxPoint > 0 ? ((totalPoint / totalMaxPoint) * 100).toFixed(1) : 'N/A';
          return (
            <Accordion.Item key={categoryName} value={categoryName}>
              <Accordion.Control>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span>{categoryName}</span>
                  &nbsp;–&nbsp;
                  <b>
                    {totalPoint}/{totalMaxPoint} Puan
                  </b>
                  <span style={{ marginLeft: 'auto' }}>({percentage}%)</span>
                </div>
              </Accordion.Control>
              <Accordion.Panel>
                {successfulCriteria.length > 0 && (
                  <Accordion variant="filled" mb="sm">
                    <Accordion.Item value="successful">
                      <Accordion.Control style={{ backgroundColor: '#e9f7ef' }}>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <span>Başarılı Kriterler</span>
                          <Badge size="sm" ml="xs" color="green">
                            {successfulCriteria.length}
                          </Badge>
                          <span style={{ marginLeft: 'auto' }}>
                            {successfulCriteria.reduce((acc, item) => acc + item.point, 0)}/
                            {successfulCriteria.reduce((acc, item) => acc + item.maxPoint, 0)} Puan
                          </span>
                        </div>
                      </Accordion.Control>
                      <Accordion.Panel>
                        {successfulCriteria.map((criterion, index) => 
                          renderCriterionCard(criterion, index, true)
                        )}
                      </Accordion.Panel>
                    </Accordion.Item>
                  </Accordion>
                )}
                {failedCriteria.length > 0 && (
                  <Accordion variant="filled">
                    <Accordion.Item value="failed">
                      <Accordion.Control style={{ backgroundColor: '#ffebee' }}>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <span>Başarısız Kriterler</span>
                          <Badge size="sm" ml="xs" color="red">
                            {failedCriteria.length}
                          </Badge>
                          <span style={{ marginLeft: 'auto' }}>
                            {failedCriteria.reduce((acc, item) => acc + item.point, 0)}/
                            {failedCriteria.reduce((acc, item) => acc + item.maxPoint, 0)} Puan
                          </span>
                        </div>
                      </Accordion.Control>
                      <Accordion.Panel>
                        {failedCriteria.map((criterion, index) => 
                          renderCriterionCard(criterion, index, false)
                        )}
                      </Accordion.Panel>
                    </Accordion.Item>
                  </Accordion>
                )}
              </Accordion.Panel>
            </Accordion.Item>
          );
        })}
      </Accordion>

      {/* Overall feedback section */}
      {showFeedbackMode && hasFeedbackPermission && (
        <Card shadow="sm" padding="lg" radius="md" withBorder style={{ marginTop: '20px' }}>
          <Text weight={500} size="lg" mb="sm">Genel Geri Bildirim</Text>
          <Textarea
            placeholder="Kalite analizi hakkında genel geri bildiriminizi yazın..."
            value={overallFeedback}
            onChange={(e) => setOverallFeedback(e.currentTarget.value)}
            minRows={3}
            mb="md"
          />
          <Group position="right">
            <Button 
              color="blue" 
              onClick={submitAllFeedback}
              disabled={feedbackItems.length === 0 && !overallFeedback.trim()}
            >
              Tüm Geri Bildirimleri Gönder ({feedbackItems.length} kriter + genel geri bildirim)
            </Button>
          </Group>
        </Card>
      )}

      {/* Feedback modal */}
      <Modal
        opened={feedbackModalOpen}
        onClose={() => setFeedbackModalOpen(false)}
        title={<Text weight={700}>Kriter için Sorun Bildirimi</Text>}
        size="lg"
        zIndex={1000}
      >
        {selectedCriterion && (
          <>
            <Text weight={500} my="xs">{selectedCriterion.controlCriterion}</Text>
            <Text color="dimmed" size="sm" mb="md">{selectedCriterion.description}</Text>
            <Text size="sm" mb="xs">Kategori: {selectedCriterion.controlCategory}</Text>
            <Text size="sm" mb="md">Mevcut Puan: {selectedCriterion.point}/{selectedCriterion.maxPoint}</Text>

            <Textarea
              label="Mantıksal Sorun Bildirimi"
              description="Bu kriter değerlendirmesinin neden mantıksal olarak yanlış olduğunu açıklayın"
              placeholder="Bu kriter yanlış görünüyor çünkü..."
              value={logicFeedback}
              onChange={(e) => setLogicFeedback(e.currentTarget.value)}
              minRows={3}
              mb="md"
            />

            <Textarea
              label="Puan Dağılımı Sorunu"
              description="Puan dağılımının neden yanlış olduğunu açıklayın"
              placeholder="Puan dağılımı şöyle olmalıydı..."
              value={pointFeedback}
              onChange={(e) => setPointFeedback(e.currentTarget.value)}
              minRows={3}
              mb="md"
            />
            
            <Group position="right" mt="md">
              <Button variant="outline" onClick={() => setFeedbackModalOpen(false)}>İptal</Button>
              <Button 
                onClick={handleFeedbackSubmit} 
                disabled={!logicFeedback.trim() && !pointFeedback.trim()}
              >
                Geri Bildirim Gönder
              </Button>
            </Group>
          </>
        )}
      </Modal>
    </>
  );
}
