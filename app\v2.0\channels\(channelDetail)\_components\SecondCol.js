'use client';
import React from 'react';
import { Tabs } from '@mantine/core';
import AddAnalysisCommentForm from '@/common/forms/AddAnalysisCommentForm';
import DetailedInformation from './DetailedInformation';
import QualityRuleErrors from './QualityRuleErrors';

export function SecondCol({ 
  detail, 
  channelType, 
  agents, 
  tenantParameters, 
  setSearchTerm, 
  id, 
  analysisResultId, 
  fetchAuthClient, 
  permissions,
  agentPercentage,
  customerPercentage,
  formatPercentage
}) {
  return (
    <Tabs defaultValue="info">
      <Tabs.List>
        <Tabs.Tab value="info">
          <b>Detaylı Bilgiler</b>
        </Tabs.Tab>
        {detail.analysis && (
          <Tabs.Tab value="qualityRuleErrors">
            <b>Kalite Kural Hataları</b>
          </Tabs.Tab>
        )}
        {detail.currentAnalysisResultId && (
          <Tabs.Tab value="comments">
            <b>Yorumlar</b>
          </Tabs.Tab>
        )}
      </Tabs.List>
      
      {/* Detay<PERSON><PERSON> Bilgiler */}
      <Tabs.Panel value="info" style={{ paddingTop: '16px' }}>
        <DetailedInformation 
          detail={detail} 
          channelType={channelType} 
          agents={agents} 
          tenantParameters={tenantParameters} 
          setSearchTerm={setSearchTerm}
          agentPercentage={agentPercentage}
          customerPercentage={customerPercentage}
          formatPercentage={formatPercentage} 
        />
      </Tabs.Panel>
      
      {detail.analysis && (
        <Tabs.Panel value="qualityRuleErrors" style={{ paddingTop: '16px' }}>
          <QualityRuleErrors 
            detail={detail} 
            permissions={permissions} 
            channelType={channelType} 
          />
        </Tabs.Panel>
      )}
      
      {detail.currentAnalysisResultId && (
        <Tabs.Panel value="comments" style={{ paddingTop: '16px' }}>
          <AddAnalysisCommentForm
            channelType={channelType}
            id={id}
            analysisResultId={analysisResultId}
            fetchAuthClient={fetchAuthClient}
            permissions={permissions}
            tenantParameters={tenantParameters}
          />
        </Tabs.Panel>
      )}
    </Tabs>
  );
}
