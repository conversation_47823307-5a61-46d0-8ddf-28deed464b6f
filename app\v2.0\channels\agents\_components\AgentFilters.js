'use client';
import React from 'react';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import SelectFilterPopover from '@/common/components/filters/SelectFilterPopover';
import { Button, Card, Group } from '@mantine/core';
import {
  IconUser,
  IconX
} from '@tabler/icons-react';

export default function AgentFilters({
  agents,
  dateFilterValue,
  agentIdFilterValue,
  initDateType,
  setFilter,
  clearFilters,
}) {
  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder style={{ position: 'sticky', top: 0, zIndex: 10 }}>
      <Group>
        <Button
          color="red"
          variant={'light'}
          onClick={clearFilters}
          leftSection={<IconX size={16} />}
          style={{ marginRight: 10 }}
        >
          Filtreleri Temizle
        </Button>
        <DateFilterPopover
          value={dateFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, 'dateFilterValue', 'date')}
          label={'<PERSON><PERSON><PERSON>'}
          initDateTypeValue={initDateType}
        />
        <SelectFilterPopover
          multiple
          value={agentIdFilterValue}
          onChange={(value, isEmpty) => setFilter(value, isEmpty, 'agentIdFilterValue', 'agentId')}
          icon={<IconUser />}
          label={'Temsilci'}
          data={
            agents.map((x) => ({
              label: x.name + ' ' + x.surname,
              value: '' + x.id,
            })) || []
          }
        />
      </Group>
    </Card>
  );
}
