
export const handlePeriodSelect = async ({
  period,
  setViewingSingleDay,
  setSelectedDate,
  setOneMonthBeforeDashboardData,
  setActivePeriod,
  allFilters,
  tenantTableRef,
  agentId,
  fetchAuthClient,
  channelType
}) => {
  setViewingSingleDay(false);
  setSelectedDate(null);
  setOneMonthBeforeDashboardData(null);
  setActivePeriod(period);
  allFilters.forEach((x) => {
    if (x.current) {
      x.current.clearFilter();
    }
  });
  const now = new Date();
  let startDate = new Date();
  if (period === 'TODAY') {
    startDate.setUTCHours(0, 0, 0, 0);
  } else if (period === '1DAY') {
    startDate.setDate(now.getDate() - 1);
  } else if (period === '1WEEK') {
    startDate.setDate(now.getDate() - 7);
  } else if (period === '1MONTH') {
    startDate.setMonth(now.getMonth() - 1);
  } else if (period === '6MONTHS') {
    startDate.setMonth(now.getMonth() - 6);
  } else if (period === '1YEAR') {
    startDate.setFullYear(now.getFullYear() - 1);
  }

  if (tenantTableRef.current) {
    tenantTableRef.current.setColumnFilters(
      agentId
        ? [
            { id: 'agentId', value: ['' + agentId] },
            {
              id: 'date',
              value: [startDate, now],
            },
          ]
        : [
            {
              id: 'date',
              value: [startDate, now],
            },
          ]
    );
  }

  const oneMonthAgo = new Date(now);
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  const dates = [oneMonthAgo.toISOString(), now.toISOString()];
  const response2 = await fetchAuthClient(channelType + '/dashboard', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify([{ id: 'date', value: dates }]),
  });
  const responseJson2 = await response2.json();
  setOneMonthBeforeDashboardData(responseJson2);
  
  return responseJson2;
};
