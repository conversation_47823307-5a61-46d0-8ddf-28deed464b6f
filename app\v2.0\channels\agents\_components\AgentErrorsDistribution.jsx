import React from 'react';
import { Card, Text, Group, Progress, Tooltip, Button, ScrollArea } from '@mantine/core';

const AgentErrorsDistribution = ({ agentFailArray, totalCount, onAgentClick }) => {
  if (!agentFailArray || agentFailArray.length === 0) {
    return (
      <Card withBorder p="md" radius="md">
        <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
          Temsilci Hata Dağılımı
        </Text>
        <Text mt="md">Veri bulunamadı</Text>
      </Card>
    );
  }

  const topAgents = [...agentFailArray]
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);

  const totalErrors = agentFailArray.reduce((sum, agent) => sum + agent.count, 0);

  const getColor = (index) => {
    const baseColors = ['#FF6B6B', '#FF9E7D', '#FFBF8E', '#F9D293', '#E5E59F', '#B5EAD7', '#C7CEEA', '#E0BBE4', '#FEC8D8', '#FFDFD3'];
    return baseColors[index % baseColors.length];
  };

  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <Text c="dimmed" size="xl" tt="uppercase" fw={700} mb="md">
        Temsilci Hata Dağılımı
      </Text>
      <Text mb="lg" size="sm" color="dimmed">
        Toplam {totalCount} temsilci arasından en çok hata yapan 10 temsilci
      </Text>

      <ScrollArea h={400}>
        {topAgents.map((agent, index) => {
          const percentage = (agent.count / totalErrors * 100).toFixed(1);
          
          return (
            <div key={agent.id} style={{ marginBottom: '15px' }}>
              <Group position="apart" mb="xs">
                <div className='flex justify-between w-full'>
                  <Text weight={500}>{agent.name}</Text>
                  <Group spacing="xs">
                    <Text size="sm">{agent.count} hata ({percentage}%)</Text>
                    <Button
                      variant="subtle"
                      size="xs"
                      onClick={() => onAgentClick(agent.id)}
                    >
                      Detaylar
                    </Button>
                  </Group>
                </div>
              </Group>
              <Tooltip label={`${agent.count} hata - ${percentage}% (${agent.name})`}>
                <Progress 
                  value={percentage} 
                  color={getColor(index)} 
                  size="lg" 
                  radius="md"
                  striped
                  animate={index === 0}
                />
              </Tooltip>
            </div>
          );
        })}
      </ScrollArea>
    </Card>
  );
};

export default AgentErrorsDistribution;
