import React from 'react';
import { Box, Card, Group, HoverCard, SimpleGrid, Text } from '@mantine/core';
import { IconRuler } from '@tabler/icons-react';

const AverageQualityScore = ({ setFilter, setPointFilterValue, dashboardData, tenantParameters, label, classes }) => {
  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <HoverCard width={350} shadow="md" withinPortal position="bottom">
        <HoverCard.Target>
          <Box style={{ cursor: 'pointer' }} className='flex flex-col justify-between h-full'>
            <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
              Ortalama Kalite Puanı
            </Text>
            <Group
              justify="space-between"
              style={{
                marginTop: 'auto',
              }}
            >
              <Group align="flex-end" gap="xs">
                <Text fz="lg" fw={700}>
                  {dashboardData.totalPointMean.toFixed(2)}
                </Text>
              </Group>
              <IconRuler size={20} className={classes.icon} stroke={1.5} />
            </Group>
          </Box>
        </HoverCard.Target>
        <HoverCard.Dropdown p="xs">
          <Text fw={600} size="sm" mb="xs">
            Detaylı Kalite Puanları
          </Text>
          <SimpleGrid cols={{ base: 1, xs: 3 }}>
            <Box
              style={{
                borderBottomColor: 'teal',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter([100, 100], false, setPointFilterValue, 'point');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                =100 Puan
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700} size="sm">
                  {dashboardData.fullPointCount.toLocaleString('tr-TR')} {label}
                </Text>
              </Group>
            </Box>
            <Box
              style={{
                borderBottomColor: 'orange',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter([0, tenantParameters.targetedQualityPoint - 1], false, setPointFilterValue, 'point');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                {'<' + tenantParameters.targetedQualityPoint} Puan
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700} size="sm">
                  {dashboardData.unPassedTargetQualityPointCount.toLocaleString('tr-TR')} {label}
                </Text>
              </Group>
            </Box>
            <Box
              style={{
                borderBottomColor: 'red',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter([0, 0], false, setPointFilterValue, 'point');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                =0 Puan
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700} size="sm">
                  {dashboardData.zeroPointCount.toLocaleString('tr-TR')} {label}
                </Text>
              </Group>
            </Box>
          </SimpleGrid>
        </HoverCard.Dropdown>
      </HoverCard>
    </Card>
  );
};

export default AverageQualityScore;
