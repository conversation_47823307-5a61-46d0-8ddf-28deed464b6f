.video-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f4f4f4;
}

.video-title {
  font-size: 2rem;
  color: #333;
  text-align: center;
}

.video-player-wrapper {
  position: relative;
  width: 100%;
  max-width: 800px;
  background-color: black;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
  overflow: hidden;
}

.video-player {
  width: 100%;
  height: auto;
}

button {
  margin-top: 10px;
}

  
  .controls {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 10px;
  }
  
  .control-btn {
    padding: 8px 15px;
    font-size: 1rem;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .control-btn:hover {
    background-color: rgba(0, 0, 0, 0.9);
  }
  
  @media (max-width: 768px) {
    .video-player-wrapper {
      width: 90%;  
    }
  
    .video-title {
      font-size: 1.5rem;
    }
  
    .control-btn {
      font-size: 0.9rem;
    }
  }
  