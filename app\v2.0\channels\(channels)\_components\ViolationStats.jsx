import React from 'react';
import { Card, Group, Text, ThemeIcon, Stack, Divider } from '@mantine/core';
import { IconBadgesFilled } from '@tabler/icons-react';

const ViolationStats = ({ setFilter, setSpecialFilterFilterValue, dashboardData, label }) => {
  return (
    <Card withBorder p="xs" radius="md" shadow="sm">
      <Group position="apart" mb={5} noWrap justify="space-between">
        <Text c="dimmed" size="xs" tt="uppercase" fw={700} lineClamp={1}>
          Küfür / Hakaret
        </Text>
        <ThemeIcon color="gray" variant="light" style={{ color: 'var(--mantine-color-red-6)' }} size={28} radius="md">
          <IconBadgesFilled size={18} stroke={1.5} />
        </ThemeIcon>
      </Group>

      <Stack spacing="xs">
        <Group spacing={4} justify="space-between">
          <div className="flex gap-2 mt-1">
            <Text size="sm" fw={600} style={{ lineHeight: 1 }}>
              Temsilci:
            </Text>
          </div>
          <Text
            size="xs"
            c="dimmed"
            className="cursor-pointer"
            onClick={() => setFilter(['agent_kufur'], false, setSpecialFilterFilterValue, 'specialFilter')}
          >
            {dashboardData.agentSwearCount} küfür,
          </Text>
          <Text
            size="xs"
            c="dimmed"
            className="cursor-pointer"
            onClick={() => setFilter(['agent_hakaret'], false, setSpecialFilterFilterValue, 'specialFilter')}
          >
            {dashboardData.agentInsultCount} hakaret,
          </Text>
          <Text
            size="xs"
            c="dimmed"
            className="cursor-pointer"
            onClick={() => setFilter(['agent_kufur_hakaret'], false, setSpecialFilterFilterValue, 'specialFilter')}
          >
            {dashboardData.agentInsultsSwearCount} küfür & hakaret
          </Text>
        </Group>

        <Divider size="xs" />

        <Group spacing={4} justify="space-between">
          <div className="flex gap-2 mt-1">
            <Text size="sm" fw={600} style={{ lineHeight: 1 }}>
              Müşteri:
            </Text>
          </div>
          <Text
            size="xs"
            c="dimmed"
            className="cursor-pointer"
            onClick={() => setFilter(['customer_kufur'], false, setSpecialFilterFilterValue, 'specialFilter')}
          >
            {dashboardData.customerSwearCount} küfür,
          </Text>
          <Text
            size="xs"
            c="dimmed"
            className="cursor-pointer"
            onClick={() => setFilter(['customer_hakaret'], false, setSpecialFilterFilterValue, 'specialFilter')}
          >
            {dashboardData.customerInsultCount} hakaret
          </Text>
          <Text
            size="xs"
            c="dimmed"
            className="cursor-pointer"
            onClick={() => setFilter(['customer_kufur_hakaret'], false, setSpecialFilterFilterValue, 'specialFilter')}
          >
            {dashboardData.customerInsultsSwearCount} küfür & hakaret
          </Text>
        </Group>
      </Stack>
    </Card>
  );
};

export default ViolationStats;
