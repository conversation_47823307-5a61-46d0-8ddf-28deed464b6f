'use client';

import { Box, Card, Collapse, Group, Paper, Table, Text, ThemeIcon, UnstyledButton } from '@mantine/core';
import React, { useState } from 'react';
import { groupDefinitions } from '../constants';
import { IconChevronDown, IconChevronUp } from '@tabler/icons-react';

const AlarmStatus = ({ dashboardDataCall, dashboardDataChat, onItemClick }) => {
  const [openedGroups, setOpenedGroups] = useState(Object.fromEntries(groupDefinitions.map((g) => [g.title, true])));
  return (
    <Card withBorder p="md" radius="md" className="h-[850px] 5xl:h-[900px] ">
      <Text c="black" size="xl" tt="uppercase" fw={700} mb="sm">
        ALARM DURUMU
      </Text>
      {(() => {
        const combinedNames = [
          ...Object.keys(dashboardDataCall.alarmCategories),
          ...Object.keys(dashboardDataChat.alarmCategories),
        ];
        const alarmCategoryNames = Array.from(new Set(combinedNames));

        const totalAlarmCategory = alarmCategoryNames.map((name) => {
          const callCount = dashboardDataCall.alarmCategories[name] || 0;
          const chatCount = dashboardDataChat.alarmCategories[name] || 0;
          const total = callCount + chatCount;
          return {
            name,
            Çağrı: callCount,
            Yazışma: chatCount,
            total,
          };
        });

        const riskOrder = { Yüksek: 3, Orta: 2, Düşük: 1 };
        totalAlarmCategory.forEach((item) => {
          if (['CİMER / Devlet Şikayeti', 'Mahkeme / Yasal Tehdit'].includes(item.name)) {
            item.riskLevel = 'Yüksek';
          } else if (['Şikayetvar / Dış Platformlar', 'Sosyal Medya Şikayeti / İfşa'].includes(item.name)) {
            item.riskLevel = 'Orta';
          } else {
            item.riskLevel = 'Düşük';
          }
          item.riskRank = riskOrder[item.riskLevel];
        });

        const groupedData = groupDefinitions.map((group) => ({
          title: group.title,
          items: totalAlarmCategory
            .filter((x) => group.keys.includes(x.name))
            .sort((a, b) => b.riskRank - a.riskRank || b.total - a.total),
        }));

        const getRiskIcon = (riskLevel) => {
          switch (riskLevel) {
            case 'Yüksek':
              return <ThemeIcon color="red" size={12} radius="xl" />;
            case 'Orta':
              return <ThemeIcon color="orange" size={12} radius="xl" />;
            case 'Düşük':
              return <ThemeIcon color="green" size={12} radius="xl" />;
            default:
              return null;
          }
        };

        return (
          <>
            {groupedData.map(({ title, items }) => (
              <Paper key={title} withBorder mb="md" radius="md" p={0} sx={{ overflow: 'hidden' }}>
                <UnstyledButton
                  onClick={() =>
                    setOpenedGroups((prev) => ({
                      ...prev,
                      [title]: !prev[title],
                    }))
                  }
                  style={{ width: '100%', padding: '12px 16px', background: '#f8f9fa' }}
                  sx={(theme) => ({
                    '&:hover': {
                      backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[1],
                    },
                  })}
                >
                  <Group position="apart">
                    <Text fw={700} color="dimmed" tt="uppercase" size="sm">
                      {title}
                    </Text>
                    {openedGroups[title] ? <IconChevronUp size={18} /> : <IconChevronDown size={18} />}
                  </Group>
                </UnstyledButton>
                {/* Collapsible Rows */}
                <Collapse in={openedGroups[title]}>
                  <Table striped highlightOnHover>
                    <Table.Thead>
                      <Table.Tr>
                        <Table.Th style={{ width: '300px' }}>Risk Kategorisi</Table.Th>
                        <Table.Th style={{ textAlign: 'center' }}>Çağrı</Table.Th>
                        <Table.Th style={{ textAlign: 'center' }}>Yazışma</Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {items.map((x) => (
                        <Table.Tr key={x.name}>
                          <Table.Td>
                            <Group spacing="xs">
                              {getRiskIcon(x.riskLevel)}
                              <Box>{x.name}</Box>
                            </Group>
                          </Table.Td>
                          <Table.Td style={{ textAlign: 'center' }}>
                            <Box
                              style={{
                                cursor: 'pointer',
                                color: 'white',
                                backgroundColor: '#1971c2',
                                padding: '4px 12px',
                                borderRadius: '4px',
                                display: 'inline-block',
                                fontWeight: 600,
                                minWidth: '60px',
                              }}
                              onClick={() => {
                                if (onItemClick) {
                                  onItemClick({
                                    type: 'Call',
                                    alarmCategory: x.name,
                                  });
                                } else {
                                  window.location.href = `/${
                                    process.env.VERSION
                                  }/channels?channelType=Call&alarmCategories=${encodeURIComponent(x.name)}`;
                                }
                              }}
                            >
                              {x.Çağrı.toLocaleString('tr-TR')}
                            </Box>
                          </Table.Td>
                          <Table.Td style={{ textAlign: 'center' }}>
                            <Box
                              style={{
                                cursor: 'pointer',
                                color: 'white',
                                backgroundColor: '#ff9500',
                                padding: '4px 12px',
                                borderRadius: '4px',
                                display: 'inline-block',
                                fontWeight: 600,
                                minWidth: '60px',
                              }}
                              onClick={() => {
                                if (onItemClick) {
                                  onItemClick({
                                    type: 'Chat',
                                    alarmCategory: x.name,
                                  });
                                } else {
                                  window.location.href = `/${
                                    process.env.VERSION
                                  }/channels?channelType=Chat&alarmCategories=${encodeURIComponent(x.name)}`;
                                }
                              }}
                            >
                              {x.Yazışma.toLocaleString('tr-TR')}
                            </Box>
                          </Table.Td>
                        </Table.Tr>
                      ))}
                    </Table.Tbody>
                  </Table>
                </Collapse>
              </Paper>
            ))}
          </>
        );
      })()}
    </Card>
  );
};

export default AlarmStatus;
