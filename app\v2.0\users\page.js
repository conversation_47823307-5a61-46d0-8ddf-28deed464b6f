'use client';
import { TenantTable } from '@/common/components/TenantTable';
import { Badge, PasswordInput, Select, TextInput, Modal, Button, MultiSelect } from '@mantine/core';
import { IMaskInput } from 'react-imask';
import React, { useContext, useRef, useState } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { IconFingerprint, IconUserCheck } from '@tabler/icons-react';
import { useForm } from '@mantine/form';
import { FormError } from '@/common/components/FormError';
import { convertToFormData, handleFormPostSubmit } from '@/common/functions/formFunctions';

export default function Users() {
  const { fetchAuthClient, permissions } = useContext(AuthContext);
  const [roles, setRoles] = useState([]);
  const [operationManagers, setOperationManagers] = useState([]);
  const [qualityManagers, setQualityManagers] = useState([]);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showPersonModal, setShowPersonModal] = useState(false);
  const userTableRef = useRef();
  const roleForm = useForm({
    mode: 'controlled',
  });
  const personForm = useForm({
    mode: 'controlled',
  });
  const handleRoleChangeFormSubmit = async (e) => {
    const formValues = roleForm.getValues();
    let formData = convertToFormData(formValues);
    handleFormPostSubmit(
      e,
      roleForm,
      fetchAuthClient('User/assignRole', {
        method: 'POST',
        body: formData,
      }),
      async (response) => {
        setShowRoleModal(false);
        userTableRef.current.fetchData();
      }
    );
  };
  const handlePersonChangeFormSubmit = async (e) => {
    const formValues = personForm.getValues();
    let formData = convertToFormData(formValues);
    if (formData.get('operationManagerIds')) {
      const operationManagerIds = formData.get('operationManagerIds').split(',').map(Number);
      formData.delete('operationManagerIds');
      operationManagerIds.forEach((id) => formData.append('OperationManagerIds', id));
    }
    if (formData.get('qualityManagerIds')) {
      const qualityManagerIds = formData.get('qualityManagerIds').split(',').map(Number);
      formData.delete('qualityManagerIds');
      qualityManagerIds.forEach((id) => formData.append('QualityManagerIds', id));
    }
    handleFormPostSubmit(
      e,
      personForm,
      fetchAuthClient('Agent/updateQualityOperationManagers', {
        method: 'POST',
        body: formData,
      }),
      async (response) => {
        setShowPersonModal(false);
        userTableRef.current.fetchData();
      }
    );
  };
  const form = (mode, tableForm) => {
    return (
      <>
        <TextInput {...tableForm.getInputProps('name')} key={tableForm.key('name')} label="İsim" withAsterisk mt="md" />
        <TextInput
          {...tableForm.getInputProps('surname')}
          key={tableForm.key('surname')}
          label="Soyisim"
          withAsterisk
          mt="md"
        />
        <TextInput
          {...tableForm.getInputProps('email')}
          key={tableForm.key('email')}
          label="E-Posta"
          withAsterisk
          mt="md"
        />
        <TextInput
          label="Telefon"
          {...tableForm.getInputProps('phone')}
          key={tableForm.key('phone')}
          mt="md"
          component={IMaskInput}
          mask="000 000 00 00"
        />
        <PasswordInput
          {...tableForm.getInputProps('password')}
          key={tableForm.key('password')}
          withAsterisk={mode === 'add'}
          mt="md"
          label="Şifre"
        />
      </>
    );
  };
  const fetchRoles = async () => {
    const response = await fetchAuthClient('Role', {
      method: 'GET',
    });
    if (response.ok) {
      var responseJson = await response.json();
      setRoles(responseJson);
    }
  };
  const fetchOperationManagers = async () => {
    const response = await fetchAuthClient('User/operationManagers', {
      method: 'GET',
    });
    if (response.ok) {
      var responseJson = await response.json();
      setOperationManagers(responseJson);
    }
  };
  const fetchQualityManagers = async () => {
    const response = await fetchAuthClient('User/qualityManagers', {
      method: 'GET',
    });
    if (response.ok) {
      var responseJson = await response.json();
      setQualityManagers(responseJson);
    }
  };
  const columns = [
    {
      id: 'nameSurname',
      accessorKey: 'nameSurname',
      header: 'Ad Soyad',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{row.original.name + ' ' + row.original.surname}</>,
    },
    {
      id: 'email',
      accessorKey: 'email',
      header: 'E-Posta',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{cell.getValue()}</>,
    },
    {
      id: 'role',
      accessorKey: 'role',
      header: 'Rol',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => (
        <Badge color="gray" radius="xs">
          {cell.getValue()}
        </Badge>
      ),
    },
  ];
  if (!permissions.includes('User.View')) {
    window.location.href = '/401';
    return <></>;
  }
  return (
    <>
      <TenantTable
        ref={userTableRef}
        allowAdd={permissions.includes('User.Add')}
        allowUpdate={(data) => permissions.includes('User.Update')}
        allowDelete={permissions.includes('User.Delete')}
        columns={columns}
        entityType="User"
        entityText="Kullanıcı"
        form={form}
        actionButtons={[
          {
            text: 'Rol Güncelle',
            isVisible: (rowData) => {
              return permissions.includes('User.UpdateRole') && rowData.role !== 'ADMİN';
            },
            icon: <IconFingerprint />,
            onClick: (rowData) => {
              roleForm.setFieldValue('id', rowData.id);
              roleForm.setFieldValue('roleName', rowData.role);
              fetchRoles().then((x) => setShowRoleModal(true));
            },
          },
          {
            text: 'Operasyon / Kalite Yetkililerini Güncelle',
            isVisible: (rowData) => {
              if(rowData.role === 'ÇAĞRI MT' && permissions.includes("Call.AgentUpdatePerson")){
                return true;
              }
              else if(rowData.role === 'YAZIŞMA MT' && permissions.includes("Chat.AgentUpdatePerson")){
                return true;
              }
              else{
                return false;
              }
            },
            icon: <IconUserCheck />,
            onClick: (rowData) => {
              personForm.setFieldValue('id', rowData.id);
              if(rowData.role === "YAZIŞMA MT"){
                personForm.setFieldValue('channelType', "Chat");
              }
              else if(rowData.role === "ÇAĞRI MT"){
                personForm.setFieldValue('channelType', "Call");
              }
              // console.log(rowData);
              if (rowData.extraJson) {
                if (rowData.extraJson.QualityManagerIds) {
                  personForm.setFieldValue(
                    'qualityManagerIds',
                    rowData.extraJson.QualityManagerIds.map((x) => '' + x)
                  );
                } else {
                  personForm.setFieldValue('qualityManagerIds', []);
                }
                if (rowData.extraJson.OperationManagerIds) {
                  personForm.setFieldValue(
                    'operationManagerIds',
                    rowData.extraJson.OperationManagerIds.map((x) => '' + x)
                  );
                } else {
                  personForm.setFieldValue('operationManagerIds', []);
                }
              } else {
                personForm.setFieldValue('qualityManagerIds', []);
                personForm.setFieldValue('operationManagerIds', []);
              }
              fetchOperationManagers().then((x) => fetchQualityManagers().then((x) => setShowPersonModal(true)));
            },
          },
        ]}
        entityToAdd={async () => {
          return {};
        }}
        entityToUpdate={async (entity) => {
          return {
            id: entity.id,
            name: entity.name,
            surname: entity.surname,
            phone: entity.phone,
            email: entity.email,
            password: '',
          };
        }}
      />
      <Modal opened={showRoleModal} onClose={() => setShowRoleModal(false)}>
        <form onSubmit={handleRoleChangeFormSubmit}>
          <Select
            {...roleForm.getInputProps('roleName')}
            key={roleForm.key('roleName')}
            label="Rol"
            data={roles.map((x) => x.name)}
            withAsterisk
          />
          <Button type="submit" fullWidth mt="md">
            Güncelle
          </Button>
          <FormError errorText={roleForm.errors['formError']} />
        </form>
      </Modal>
      <Modal opened={showPersonModal} onClose={() => setShowPersonModal(false)}>
        <form onSubmit={handlePersonChangeFormSubmit}>
          <MultiSelect
            {...personForm.getInputProps('operationManagerIds')}
            key={personForm.key('operationManagerIds')}
            label="Operasyon Yetkilileri"
            data={operationManagers.map((x) => ({ label: x.name + ' ' + x.surname, value: '' + x.id }))}
          />
          <MultiSelect
            {...personForm.getInputProps('qualityManagerIds')}
            key={personForm.key('qualityManagerIds')}
            label="Kalite Yetkilileri"
            data={qualityManagers.map((x) => ({ label: x.name + ' ' + x.surname, value: '' + x.id }))}
            mt="md"
          />
          <Button type="submit" fullWidth mt="md">
            Güncelle
          </Button>
          <FormError errorText={personForm.errors['formError']} />
        </form>
      </Modal>
    </>
  );
}
