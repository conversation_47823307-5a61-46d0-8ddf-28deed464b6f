'use client';
import React from 'react';
import PropTypes from 'prop-types';
import { Card, Text } from '@mantine/core';
import { Line } from 'react-chartjs-2';

const getSentimentEmoji = (sentimentScore) => {
  if (sentimentScore === null || sentimentScore === undefined) return '';
  const score = Math.round(sentimentScore);
  if (score < 50) return '😞';
  if (score === 50) return '😐';
  if (score > 50 && score <= 70) return '😊';
  if (score > 70) return '😄';
  return '';
};

export const SentimentChart = React.memo(function SentimentChart({ formatTime, prepareSentimentChartData }) {
  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder mt="md">
      <Text fw={700} mb="sm">
        Temsilci & Müşteri Duygu Durumu Grafiği
      </Text>
      {(() => {
        const chartData = prepareSentimentChartData();

        if (!chartData || (chartData.agentData.length === 0 && chartData.customerData.length === 0)) {
          return (
            <Text color="dimmed" align="center">
              Duygu durumu verisi bulunmamaktadır.
            </Text>
          );
        }

        const data = {
          datasets: [
            {
              label: 'Temsilci',
              data: chartData.agentData,
              borderColor: 'rgba(54, 162, 235, 1)',
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              fill: false,
              tension: 0.4,
              pointRadius: 4,
              pointHoverRadius: 6,
              pointBackgroundColor: 'rgba(54, 162, 235, 1)',
              pointBorderColor: '#fff',
              pointBorderWidth: 2,
            },
            {
              label: 'Müşteri',
              data: chartData.customerData,
              borderColor: 'rgba(75, 192, 102, 1)',
              backgroundColor: 'rgba(75, 192, 102, 0.2)',
              fill: false,
              tension: 0.4,
              pointRadius: 4,
              pointHoverRadius: 6,
              pointBackgroundColor: 'rgba(75, 192, 102, 1)',
              pointBorderColor: '#fff',
              pointBorderWidth: 2,
            },
          ],
        };

        const options = {
          responsive: true,
          maintainAspectRatio: false,
          parsing: {
            xAxisKey: 'x',
            yAxisKey: 'y',
          },
          scales: {
            x: {
              type: 'linear',
              title: {
                display: true,
                text: 'Konuşma Zamanı (saniye)',
                font: {
                  size: 14,
                  weight: 'bold',
                },
              },
              min: chartData.startTime,
              max: chartData.endTime,
              ticks: {
                callback: function (value) {
                  return formatTime(value);
                },
              },
            },
            y: {
              min: 0,
              max: 100,
              title: {
                display: true,
                text: 'Duygu Durumu Skoru',
                font: {
                  size: 14,
                  weight: 'bold',
                },
              },
              ticks: {
                stepSize: 20,
              },
            },
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                usePointStyle: true,
                boxWidth: 10,
                font: {
                  size: 12,
                },
              },
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              titleColor: '#333',
              bodyColor: '#666',
              borderColor: '#ddd',
              borderWidth: 1,
              cornerRadius: 6,
              boxPadding: 6,
              usePointStyle: true,
              callbacks: {
                title: function (tooltipItems) {
                  const item = tooltipItems[0];
                  const datasetIndex = item.datasetIndex;
                  return datasetIndex === 0 ? 'Temsilci' : 'Müşteri';
                },
                label: function (context) {
                  const dataPoint = context.raw;
                  return [
                    `Duygu Skoru: ${dataPoint.y}%  ${getSentimentEmoji(dataPoint.y)}`,
                    `Zaman: ${formatTime(dataPoint.x)} (${dataPoint.timeRange})`,
                    `Mesaj: ${dataPoint.message.substring(0, 50)}${dataPoint.message.length > 50 ? '...' : ''}`,
                  ];
                },
              },
            },
          },
          interaction: {
            mode: 'nearest',
            intersect: false,
          },
          elements: {
            line: {
              borderWidth: 2,
            },
          },
        };

        return (
          <div style={{ height: '350px' }}>
            <Line data={data} options={options} />
          </div>
        );
      })()}
    </Card>
  );
});

SentimentChart.propTypes = {
  formatTime: PropTypes.func.isRequired,
  prepareSentimentChartData: PropTypes.func.isRequired,
};
