public class AgentDashboardResponse
{
public int TotalCount { get; set; }
public List<object> AgentFailArray { get; set; } = new List<object>();
public List<object> AgentPromptArray { get; set; } = new List<object>();
public List<object> AgentPointListObj { get; set; } = new List<object>();

totalCount olarak temsilci sayısı gelicek
AgentFailArray de bir agentın kaç tane kural hatası yaptığını gösteriyor
AgentPromptArray ise kural hatalarında agentların kaçar tane hata yaptğı
agentPointListObj de ise agentın toplam puan ortalaması

örnek body:
{
"totalCount": 76,
"agentFailArray": [
{
"id": 6322,
"name": "SERKAN KAHRAMAN",
"count": 13167
},
//...
{
"id": 183,
"name": "SAMED HASDEMIR",
"count": 2
}
],
"agentPromptArray": [
{
"qualityName": "Yasaklı Kelimeler/ Anlatım Dili/ Günlük Dil Kullanımı/ Diksiyon",
"agents": [
{
"id": 6322,
"name": "SERKAN KAHRAMAN",
"count": 2455
},
//...
{
"id": 185,
"name": "MEHMET ATESLIOGLU",
"count": 1
}
]
},
//...
{
"qualityName": "Bekletme Kriterlerinin Dışına Çıkılması",
"agents": [
{
"id": 6320,
"name": "Bekir Serdar KALKİM",
"count": 1654
},
//...
{
"id": 183,
"name": "SAMED HASDEMIR",
"count": 1
}
]
},
{
"qualityName": "Açılış Anonsu Doğru Yapıldı Mı?",
"agents": [
{
"id": 6320,
"name": "Bekir Serdar KALKİM",
"count": 1168
},
//...
{
"id": 238,
"name": "SİMAY AKPINAR",
"count": 1
}
]
},
{
"qualityName": "Kapanış / Son yardım önerisi sunuldu mu?",
"agents": [
{
"id": 6320,
"name": "Bekir Serdar KALKİM",
"count": 1114
},
//...
{
"id": 180,
"name": "Kadriye OGUZ",
"count": 1
}
]
},
{
"qualityName": "İsimle Hitap Etme/İsim Soy isim Alma",
"agents": [
{
"id": 6320,
"name": "Bekir Serdar KALKİM",
"count": 1073
},
//...
{
"id": 185,
"name": "MEHMET ATESLIOGLU",
"count": 1
}
]
},
{
"qualityName": "Polemik/İnisiyatif Dışı Tavır/Tartışma",
"agents": [
{
"id": 6322,
"name": "SERKAN KAHRAMAN",
"count": 72
},
//...
{
"id": 6323,
"name": "ZEYNEP SARI",
"count": 10
}
]
},
{
"qualityName": "Empatik Yaklaşım/ Pozitif İçten İletişim",
"agents": [
{
"id": 6320,
"name": "Bekir Serdar KALKİM",
"count": 190
},
//...
{
"id": 180,
"name": "Kadriye OGUZ",
"count": 1
}
]
},
{
"qualityName": "Çalışanların Birbiri Hakkında Olumsuz İfadeler Kullanılması/Kurum İmajının Sarsıcı Söylemler",
"agents": [
{
"id": 6320,
"name": "Bekir Serdar KALKİM",
"count": 58
},
//...
{
"id": 253,
"name": "SEMRA TİMUBOĞA",
"count": 1
}
]
}
],
"agentPointListObj": [
{
"id": 209,
"name": "SEMANUR EFE",
"score": 90
},
//...
{
"id": 228,
"name": "BEYZANUR KOCAK",
"score": 97
}
]
}

path bu olacak: /Agent/dashboard/{channelType}

body olarak dashboarddaki ile aynı ColumnFilters alıyor .

/Call/agent-detail/{id}
/Chat/agent-detail/{id}
burda id agentId gelecek

burdan gelcek örnek body ise:
{
"meanOfCustomerSentiment": 48.109848,
"meanOfAgentSentiment": 54.961742,
"happyCustomerCount": 1894,
"unHappyCustomerCount": 2120,
"agent": {
    "id": 6320,
    "name": "Bekir Serdar",
    "surname": "KALKİM",
    "email": "<EMAIL>",
    "phone": null,
    "imageFile": "",
    "extraJson": null,
    "role": "ÇAĞRI MT"
},
"qualityManagers": [],
"operationManagers": [],
"categoryMap": {
    "Mağazacılık Müşteri Memnuniyet Aramaları": 130,
    "Teslimat": 745,
    "Kampanyalar": 252,
    "Diğer": 177,
    "Sipariş İşlemleri": 443,
    "Mağaza": 398,
    "Üyelik": 149,
    "Genel Sipariş Sorunları": 599,
    "İade": 956,
    "Ödemeler": 353
},
"subCategoryMap": {
    "Siparişimde takım ürünümün parçası gelmedi": 85,
    //...
    "Mağaza ürün transfer işlemleri": 70
},
"agentSwearCount": 2,
"agentInsultCount": 0,
"customerSwearCount": 42,
"customerInsultCount": 141,
"totalPointMean": 88.17111,
"totalCount": 4247,
"meanOfDuration": 287.4164,
"maxOfDuration": 1768.52,
"minOfDuration": 1.72,
"meanOfSlience": 28.265347,
"maxOfSlience": 613.4,
"minOfSlience": 0.65999985,
"analysisCompletedCount": 4202,
"analysisUnCompletedCount": 45,
"zeroPointCount": 120,
"fullPointCount": 135,
"bannedWordCount": 3363,
"slienceCount": 1145,
"passedTargetQualityPointCount": 1743,
"unPassedTargetQualityPointCount": 2459,
"passMaxACHTCount": 1585,
"betweenMaxMinACHTCount": 1416,
"unPassMinACHTCount": 1226,
"alarmCategories": {
    "CİMER / Devlet Şikayeti": 14,
    //..
    "Tüketici Hakem Heyeti / BTK": 65
},
"countByDate": {
    "2025-01-02T00:00:00Z": 41,
    //...
    "2025-05-15T00:00:00Z": 69
},
"pointMeanByDate": {
    "2025-01-02T00:00:00Z": 92.07317,
    //...
    "2025-05-15T00:00:00Z": 90.49275
},
"promptFailDict": {
    "Bekletme Kriterlerinin Dışına Çıkılması": 1654,
    //...
    "Çalışanların Birbiri Hakkında Olumsuz İfadeler Kullanılması/Kurum İmajının Sarsıcı Söylemler": 58
},
"promptSuccesDict": {
    "Ankete Yönlendirme": 3885,
    //...
    "Yasaklı Kelimeler/ Anlatım Dili/ Günlük Dil Kullanımı/ Diksiyon": 839
},
"keywords": {
    "EIVA": 1,
    //...
    "AİSİVA 2": 1
}
}
