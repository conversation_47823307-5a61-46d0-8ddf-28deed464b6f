import React, { useState, useMemo } from 'react';
import { Card, Text, Badge, Button, TextInput, Group } from '@mantine/core';
import { IconSearch } from '@tabler/icons-react';
import { MantineReactTable } from 'mantine-react-table';

const AgentPerformanceTable = ({ agentPointListObj, agentFailArray, agentPromptArray, onAgentClick }) => {
  const [globalFilter, setGlobalFilter] = useState('');

  const hasData = agentPointListObj && agentPointListObj.length > 0;
  
  const combinedData = useMemo(() => {
    if (!hasData) return [];
    
    return agentPointListObj.map((agent) => {
      const agentFailData = agentFailArray?.find(a => a.id === agent.id);
      const errorCount = agentFailData ? agentFailData.count : 0;
      
      let mostCommonError = 'Yok';
      let maxErrorCount = 0;
      
      if (agentPromptArray) {
        agentPromptArray.forEach(rule => {
          const agentInRule = rule.agents.find(a => a.id === agent.id);
          if (agentInRule && agentInRule.count > maxErrorCount) {
            maxErrorCount = agentInRule.count;
            mostCommonError = rule.qualityName;
          }
        });
      }
      
      return {
        ...agent,
        errorCount,
        mostCommonError,
        maxErrorCount
      };
    });
  }, [agentPointListObj, agentFailArray, agentPromptArray, hasData]);

  const columns = useMemo(() => [
    {
      accessorKey: 'name',
      header: 'Temsilci Adı',
      size: 200,
    },
    {
      accessorKey: 'score',
      header: 'Skor',
      size: 120,
      Cell: ({ cell }) => (
        <Badge
          color={cell.getValue() >= 90 ? 'green' : cell.getValue() >= 80 ? 'blue' : cell.getValue() >= 70 ? 'yellow' : 'red'}
          variant="filled"
        >
          {cell.getValue().toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}
        </Badge>
      ),
    },
    {
      accessorKey: 'errorCount',
      header: 'Toplam Hata',
      size: 150,
      Cell: ({ cell }) => (
        <Badge
          color={cell.getValue() === 0 ? 'green' : cell.getValue() < 10 ? 'blue' : cell.getValue() < 100 ? 'yellow' : 'red'}
          variant="outline"
        >
          {cell.getValue().toLocaleString('tr-TR')}
        </Badge>
      ),
    },
    {
      accessorKey: 'mostCommonError',
      header: 'En Sık Yapılan Hata',
      size: 250,
      Cell: ({ row }) => {
        const maxErrorCount = row.original.maxErrorCount;
        const mostCommonError = row.original.mostCommonError;
        return maxErrorCount > 0 ? (
          <Text size="sm">
            {mostCommonError.length > 30 
              ? `${mostCommonError.substring(0, 30)}...` 
              : mostCommonError}
            {maxErrorCount > 0 && ` (${maxErrorCount.toLocaleString('tr-TR')})`}
          </Text>
        ) : 'Hata yok';
      },
    },
    {
      accessorKey: 'actions',
      header: 'İşlemler',
      size: 150,
      enableSorting: false,
      Cell: ({ row }) => (
        <Button 
          variant="outline" 
          size="xs"
          onClick={() => onAgentClick(row.original.id)}
        >
          Detaya Git
        </Button>
      ),
    },
  ], [onAgentClick]);

  if (!hasData) {
    return (
      <Card withBorder p="md" radius="md">
        <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
          Temsilci Performans Tablosu
        </Text>
        <Text mt="md">Veri bulunamadı</Text>
      </Card>
    );
  }

  return (
    <Card withBorder p="md" radius="md">
      <Group position="apart" mb="md">
        <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
          Temsilci Performans Tablosu
        </Text>
        
        <TextInput
          placeholder="Temsilci ara..."
          value={globalFilter ?? ''}
          onChange={(e) => setGlobalFilter(e.target.value)}
          icon={<IconSearch size={16} />}
          style={{ width: '250px' }}
        />
      </Group>
      
      <MantineReactTable
        columns={columns}
        data={combinedData}
        enableColumnOrdering
        enableGlobalFilter={true}
        manualFiltering={false}
        onGlobalFilterChange={setGlobalFilter}
        globalFilterFn="contains"
        initialState={{ 
          sorting: [{ id: 'score', desc: true }],
          density: 'xs'
        }}
        mantineTableProps={{
          striped: true,
          highlightOnHover: true,
        }}
        mantineTableBodyCellProps={{
          align: 'center',
        }}
        mantineTableHeadCellProps={{
          align: 'center',
        }}
        mantinePaginationProps={{
          rowsPerPageOptions: ['10', '25', '50', '100'],
        }}
        enableTopToolbar={false}
        state={{ globalFilter }}
        enableColumnFilters={false}
        enableRowActions={false}
        positionActionsColumn="last"
        mantineBottomToolbarProps={{
          sx: { backgroundColor: '#f9fafb' }
        }}
        mantinePaperProps={{
          shadow: '0',
          withBorder: false,
        }}
      />
    </Card>
  );
};

export default AgentPerformanceTable;
