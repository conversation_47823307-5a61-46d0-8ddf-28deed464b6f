

export const verticalLinePlugin = {
  id: 'verticalLineOnHover',
  afterDraw: (chart) => {
    if (
      chart.tooltip &&
      chart.tooltip._active &&
      chart.tooltip._active.length
    ) {
      const ctx = chart.ctx;
      ctx.save();
      const activePoint = chart.tooltip._active[0];
      ctx.beginPath();
      ctx.setLineDash([3, 3]);
      ctx.moveTo(activePoint.element.x, chart.chartArea.top);
      ctx.lineTo(activePoint.element.x, chart.chartArea.bottom);
      ctx.lineWidth = 1;
      ctx.strokeStyle = 'rgba(120, 120, 120, 0.6)';
      ctx.stroke();
      ctx.restore();
    }
  }
};
