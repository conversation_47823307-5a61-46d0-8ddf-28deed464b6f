export const getChartJsOptions = (params) => {
  const { 
    channelType, 
    handleDateClick = () => {}, // Default no-op function if not provided
    label 
  } = params;

  return {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    hover: {
      mode: 'nearest',
      intersect: false
    },
    onClick: handleDateClick,
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: `${label} Puanı`,
          color: 'rgba(74, 144, 226, 1)',
          font: {
            weight: 'bold'
          }
        },
        grid: {
          drawOnChartArea: true,
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          precision: 0,
        },
        min: 0,
        suggestedMin: 0
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: `${label} Sayısı`,
          color: 'rgba(80, 227, 194, 1)',
          font: {
            weight: 'bold'
          }
        },
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          precision: 0,
        },
        min: 0,
        suggestedMin: 0
      },
      x: {
        grid: {
          display: false
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          boxWidth: 10,
          padding: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: 'rgba(200, 200, 200, 0.75)',
        borderWidth: 1,
        padding: 10,
        boxPadding: 5,
        cornerRadius: 4,
        boxWidth: 10,
        usePointStyle: true,
        callbacks: {
          title: (tooltipItems) => {
            return tooltipItems[0].label;
          },
          label: (context) => {
            const datasetLabel = context.dataset.label || '';
            const value = context.parsed.y;
            const formattedValue = context.datasetIndex === 0 ? value.toFixed(2) : value.toFixed(0);
            return `${datasetLabel}: ${formattedValue}`;
          }
        }
      },
      crosshair: {
        line: {
          color: '#808080',
          width: 1,
          dashPattern: [5, 5]
        },
        sync: {
          enabled: true,
          group: 1,
          suppressTooltips: false
        },
        zoom: {
          enabled: false
        }
      },
      verticalLineOnHover: {
        enabled: true
      }
    },
    maintainAspectRatio: false,
  };
};
