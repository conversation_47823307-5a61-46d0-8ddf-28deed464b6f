import React from 'react';
import { Card, Group, Text, SimpleGrid, Box, ThemeIcon } from '@mantine/core';
import { IconBadgeFilled, IconBan, IconClock, IconVolumeOff } from '@tabler/icons-react';

const DashboardCard = ({
  title,
  value,
  diffDisplay,
  comparisonText,
  icon,
  stats,
  onStatClick,
  statColors,
}) => {
  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
        {title}
      </Text>
      <Group justify="space-between" style={{ marginTop: 'auto' }}>
        <Group align="flex-end" gap="xs">
          <Text fz="xl" fw={700}>
            {value}
          </Text>
          {diffDisplay && <>{diffDisplay}</>}
        </Group>
        {icon && <ThemeIcon color="gray" variant="light" size={38} radius="md">{icon}</ThemeIcon>}
      </Group>
      {comparisonText && (
        <Text c="dimmed" fz="sm">
          {comparisonText}
        </Text>
      )}
      {stats && (
        <SimpleGrid cols={{ base: 1, md: stats.length }} mt="xl">
          {stats.map((stat, index) => (
            <Box
              key={index}
              style={{
                borderBottomColor: statColors[index] || 'gray',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              onClick={() => onStatClick && onStatClick(index)}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                {stat.label}
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700}>{stat.value}</Text>
              </Group>
            </Box>
          ))}
        </SimpleGrid>
      )}
    </Card>
  );
};

export default DashboardCard;