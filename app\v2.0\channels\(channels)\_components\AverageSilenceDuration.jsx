import React from 'react';
import { Box, Card, Group, HoverCard, SimpleGrid, Text } from '@mantine/core';
import { IconVolumeOff } from '@tabler/icons-react';

const AverageSilenceDuration = ({
  setFilter,
  setMaxSlienceFilterValue,
  dashboardData,
  tenantParameters,
  label,
  classes,
}) => {
  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <HoverCard width={350} shadow="md" withinPortal position="bottom">
        <HoverCard.Target>
          <Box style={{ cursor: 'pointer' }} className='flex flex-col justify-between h-full'>
            <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
              Ortalama Sessizlik Süresi
            </Text>
            <Group
              justify="space-between"
              style={{
                marginTop: 'auto',
              }}
            >
              <Group align="flex-end" gap="xs" style={{}}>
                <Text fz="lg" fw={700}>
                  {Math.round(dashboardData.meanOfSlience)} sn
                </Text>
              </Group>
              <IconVolumeOff size={20} className={classes.icon} stroke={1.5} />
            </Group>
          </Box>
        </HoverCard.Target>
        <HoverCard.Dropdown p="xs">
          <Text fw={600} size="sm" mb="xs">
            Detaylı Sessizlik İstatistikleri
          </Text>
          <SimpleGrid cols={{ base: 1, md: 2 }}>
            <Box
              style={{
                borderBottomColor: 'teal',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter([dashboardData.maxOfSlience, 10000], false, setMaxSlienceFilterValue, 'maxSlience');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                En Uzun
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700} size="sm">
                  {Math.round(dashboardData.maxOfSlience)} sn
                </Text>
              </Group>
            </Box>
            <Box
              style={{
                borderBottomColor: 'orange',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter([tenantParameters.slience + 1, 10000], false, setMaxSlienceFilterValue, 'maxSlience');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                {'>'}
                {tenantParameters.slience} Sn
              </Text>
              <Group justify="space-between" align="flex-end" gap={0} style={{ marginTop: 'auto' }}>
                <Text fw={700} size="sm">
                  {dashboardData.slienceCount.toLocaleString('tr-TR')} {label}
                </Text>
              </Group>
            </Box>
          </SimpleGrid>
        </HoverCard.Dropdown>
      </HoverCard>
    </Card>
  );
};

export default AverageSilenceDuration;
