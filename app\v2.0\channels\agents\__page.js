'use client';

import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { TenantTable } from '@/common/components/TenantTable';
import { useSearchParams } from 'next/navigation';

export default function Agents() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const searchParams = useSearchParams();
  const channelType = searchParams.get('channelType');
  const [agents, setAgents] = useState(null);
  const fetchAgents = async () => {
    const response = await fetchAuthClient('Agent/' + channelType, { method: 'GET' });
    if (response.ok) {
      const data = await response.json();
      setAgents(data);
    }
  };
  useEffect(() => {
    fetchAgents();
  }, [channelType]);
  const columns = [
    {
      id: 'name',
      accessorKey: 'name',
      header: 'Ad',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{cell.getValue()} </>,
    },
    {
      id: 'surname',
      accessorKey: 'surname',
      header: 'Soyad',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{cell.getValue()} </>,
    },
    {
      id: 'email',
      accessorKey: 'email',
      header: 'E-Posta',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => <>{cell.getValue()} </>,
    },
    {
      id: 'provider',
      accessorKey: 'provider',
      header: 'Sağlayıcı',
      enableSorting: false,
      enableColumnFilter: false,
      Cell: ({ cell, row }) => {
        if (row.original.extraJson && row.original.extraJson.Insider) {
          return <>Insider</>;
        } else {
          return <>Manuel</>;
        }
      },
    },
  ];
  if (!permissions.includes(channelType + '.AgentView')) {
    window.location.href = '/401';
    return <></>;
  }
  return (
    <div className="p-6">
      <TenantTable
        onRowClick={(data) => {
          window.location.href = '/v2.0/channels?channelType=' + channelType + '&agentId=' + data.id;
        }}
        urlParameters={'/' + channelType}
        allowAdd={false}
        allowUpdate={(data) => false}
        allowDelete={false}
        columns={columns}
        entityType="Agent"
        entityText="Temsilci"
      />
    </div>
  );
}
