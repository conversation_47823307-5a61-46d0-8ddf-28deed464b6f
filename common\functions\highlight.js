export function highlightSearchTerm(text, term) {
  if (!term) return text;
  const lowerText = text.toLocaleLowerCase("tr");
  const lowerTerm = term.toLocaleLowerCase("tr");
  const parts = lowerText.split(lowerTerm);
  let currentIndex = 0;
  return parts.map((part, index) => {
    const originalPart = text.slice(currentIndex, currentIndex + part.length);
    currentIndex += part.length;
    if (index < parts.length - 1) {
      const originalTerm = text.slice(currentIndex, currentIndex + term.length);
      currentIndex += term.length;
      return (
        <span key={index}>
          {originalPart}
          <span
            style={{
              backgroundColor: "yellow",
              color: "black",
              paddingLeft: "8px",
              paddingRight: "8px",
              borderRadius: "8px",
              textDecoration: "underline",
            }}
          >
            {originalTerm}
          </span>
        </span>
      );
    }
    return originalPart;
  });
}

export function highlightSearchTerm2(text, terms) {
  if (!terms || terms.length === 0) return text;

  // Tüm terimleri OR operatörü ile birleştiren regex oluştur
  const regexPattern = terms.map(term => `\\b${term}\\b`).join('|');
  const regex = new RegExp(regexPattern, 'gi');

  // Metin içindeki tüm eşleşmelerin başlangıç indexlerini bul
  const matches = Array.from(text.matchAll(regex));
  if (matches.length === 0) return text;

  const result = [];
  let lastIndex = 0;

  matches.forEach((match, idx) => {
    // Eşleşmeden önceki metni ekle
    const beforeMatch = text.slice(lastIndex, match.index);
    if (beforeMatch) {
      result.push(<span key={`text-${idx}`}>{beforeMatch}</span>);
    }

    // Highlight yapılacak kelimeyi ekle
    result.push(
      <span
        key={`highlight-${idx}`}
        style={{
          backgroundColor: "yellow",
          color: "black",
          paddingLeft: "8px",
          paddingRight: "8px",
          borderRadius: "8px",
          textDecoration: "underline",
        }}
      >
        {match[0]}
      </span>
    );

    lastIndex = match.index + match[0].length;
  });

  // Son parçayı ekle
  if (lastIndex < text.length) {
    result.push(
      <span key={`text-last`}>
        {text.slice(lastIndex)}
      </span>
    );
  }

  return result;
}