import { formatChartData } from '../chartUtils';

export const formatChartDataForChartJS = (dashboardData, label) => {
  const formattedData = formatChartData(dashboardData);

  return {
    labels: formattedData.map((item) => item.date),
    datasets: [
      {
        label: `${label} Puanı`,
        data: formattedData.map((item) => item.score),
        borderColor: 'rgba(74, 144, 226, 1)',
        backgroundColor: 'rgba(216, 223, 230, 0.2)',
        fill: true,
        tension: 0.4,
        yAxisID: 'y',
        pointRadius: 0,
        pointHoverRadius: 6,
        pointBackgroundColor: 'rgba(74, 144, 226, 1)',
        pointHoverBackgroundColor: 'rgba(74, 144, 226, 1)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
      },
      {
        label: `${label} Sayısı`,
        data: formattedData.map((item) => item.count),
        borderColor: 'rgba(80, 227, 194, 1)',
        backgroundColor: 'rgba(80, 227, 194, 0.2)',
        fill: true,
        tension: 0.4,
        yAxisID: 'y1',
        pointRadius: 0,
        pointHoverRadius: 6,
        pointBackgroundColor: 'rgba(80, 227, 194, 1)',
        pointHoverBackgroundColor: 'rgba(80, 227, 194, 1)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
      },
    ],
  };
};
