import React from 'react';
import { Card, Text, Accordion, Group, Button, Badge, Progress, Tooltip, ScrollArea } from '@mantine/core';
import { IconAlertTriangle, IconUser } from '@tabler/icons-react';

const AgentQualityRuleErrors = ({ agentPromptArray, onAgentClick }) => {
  if (!agentPromptArray || agentPromptArray.length === 0) {
    return (
      <Card withBorder p="md" radius="md">
        <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
          Kalite Kural Hataları Dağılımı
        </Text>
        <Text mt="md">Veri bulunamadı</Text>
      </Card>
    );
  }

  // Sort quality rules by total error count
  const sortedRules = [...agentPromptArray].sort((a, b) => {
    const aTotal = a.agents.reduce((sum, agent) => sum + agent.count, 0);
    const bTotal = b.agents.reduce((sum, agent) => sum + agent.count, 0);
    return bTotal - aTotal;
  });

  // Colors for the progress bars
  const colors = [
    '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50', 
    '#8BC34A', '#CDDC39', '#FDD835', '#3F51B5', '#9C27B0', '#673AB7'
  ];

  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <Text c="dimmed" size="xl" tt="uppercase" fw={700} mb="md">
        Kalite Kural Hataları Dağılımı
      </Text>

      <ScrollArea h={430}>
        <Accordion>
          {sortedRules.map((rule, index) => {
            const totalErrors = rule.agents.reduce((sum, agent) => sum + agent.count, 0);
            const color = colors[index % colors.length];
            
            return (
              <Accordion.Item key={rule.qualityName} value={rule.qualityName}>
                <Accordion.Control>
                  <Group position="apart">
                    <div className='flex w-full justify-between'>
                      <Group>
                        <IconAlertTriangle color={color} />
                        <Tooltip label={rule.qualityName} disabled={rule.qualityName.length <= 35}>
                          <Text weight={500} style={{
                            maxWidth: '350px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {rule.qualityName}
                          </Text>
                        </Tooltip>
                      </Group>
                      <Badge color="red" variant="light">
                        {totalErrors} Hata
                      </Badge>
                    </div>
                  </Group>
                </Accordion.Control>
                <Accordion.Panel>
                  {rule.agents
                    .sort((a, b) => b.count - a.count)
                    .slice(0, 10)
                    .map((agent) => {
                      const percentage = (agent.count / totalErrors * 100).toFixed(1);
                      
                      return (
                        <Group key={agent.id} position="apart" mb="sm">
                          <div className='flex w-full justify-between'>
                            <Group >
                              <IconUser size={16} />
                              <Text size="sm">{agent.name}</Text>
                            </Group>
                            <Group position="apart" style={{ flex: 1, maxWidth: '60%', justifyContent: 'end' }} >
                             {/* <Text size="xs" fw={500} c="dimmed" mr={5}>
                                {agent.count} hata ({percentage}%)
                              </Text> */}
                              <Tooltip label={`${agent.count} hata (${percentage}%)`}>
                                <Progress
                                  value={percentage}
                                  color={color}
                                  size="sm"
                                  style={{ width: '50%', marginRight: '10px' }}
                                  className='hover:cursor-pointer'
                                />
                              </Tooltip>
                              <Button
                                variant="subtle"
                                size="xs"
                                onClick={() => onAgentClick(agent.id)}
                              >
                                Detaylar
                              </Button>
                            </Group>
                          </div>
                        </Group>
                      );
                    })}
                </Accordion.Panel>
              </Accordion.Item>
            );
          })}
        </Accordion>
      </ScrollArea>
    </Card>
  );
};

export default AgentQualityRuleErrors;
